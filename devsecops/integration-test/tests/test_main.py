"""Tests for the main module."""

import pytest

from integration_test.main import Calculator, greet


class TestCalculator:
    """Test cases for Calculator class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.calc = Calculator()

    def test_add_positive_numbers(self):
        """Test addition of positive numbers."""
        assert self.calc.add(2, 3) == 5
        assert self.calc.add(10, 20) == 30

    def test_add_negative_numbers(self):
        """Test addition of negative numbers."""
        assert self.calc.add(-2, -3) == -5
        assert self.calc.add(-10, 5) == -5

    def test_add_floats(self):
        """Test addition of float numbers."""
        assert self.calc.add(2.5, 3.7) == pytest.approx(6.2)
        assert self.calc.add(0.1, 0.2) == pytest.approx(0.3)

    def test_subtract_positive_numbers(self):
        """Test subtraction of positive numbers."""
        assert self.calc.subtract(10, 3) == 7
        assert self.calc.subtract(20, 5) == 15

    def test_subtract_negative_numbers(self):
        """Test subtraction with negative numbers."""
        assert self.calc.subtract(-5, -3) == -2
        assert self.calc.subtract(5, -3) == 8

    def test_multiply_positive_numbers(self):
        """Test multiplication of positive numbers."""
        assert self.calc.multiply(3, 4) == 12
        assert self.calc.multiply(5, 6) == 30

    def test_multiply_by_zero(self):
        """Test multiplication by zero."""
        assert self.calc.multiply(5, 0) == 0
        assert self.calc.multiply(0, 10) == 0

    def test_divide_positive_numbers(self):
        """Test division of positive numbers."""
        assert self.calc.divide(10, 2) == 5.0
        assert self.calc.divide(15, 3) == 5.0

    def test_divide_by_zero(self):
        """Test division by zero raises exception."""
        with pytest.raises(ZeroDivisionError, match="Cannot divide by zero"):
            self.calc.divide(10, 0)

    def test_invalid_input_types(self):
        """Test that invalid input types raise TypeError."""
        with pytest.raises(TypeError):
            self.calc.add("2", 3)
        
        with pytest.raises(TypeError):
            self.calc.subtract(2, "3")
        
        with pytest.raises(TypeError):
            self.calc.multiply(None, 5)
        
        with pytest.raises(TypeError):
            self.calc.divide(5, [])


class TestGreet:
    """Test cases for greet function."""

    def test_greet_valid_name(self):
        """Test greeting with valid name."""
        assert greet("Alice") == "Hello, Alice!"
        assert greet("Bob") == "Hello, Bob!"
        assert greet("") == "Hello, !"

    def test_greet_invalid_input(self):
        """Test greeting with invalid input types."""
        with pytest.raises(TypeError, match="Name must be a string"):
            greet(123)
        
        with pytest.raises(TypeError, match="Name must be a string"):
            greet(None)
        
        with pytest.raises(TypeError, match="Name must be a string"):
            greet(["Alice"])
