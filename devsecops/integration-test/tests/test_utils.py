"""Tests for the utils module."""

import pytest

from integration_test.utils import format_number, is_even, is_prime, validate_input


class TestValidateInput:
    """Test cases for validate_input function."""

    def test_valid_inputs(self):
        """Test that valid inputs don't raise exceptions."""
        validate_input(5)
        validate_input(3.14)
        validate_input(-10)
        validate_input(0)

    def test_invalid_inputs(self):
        """Test that invalid inputs raise TypeError."""
        with pytest.raises(TypeError):
            validate_input("5")
        
        with pytest.raises(TypeError):
            validate_input(None)
        
        with pytest.raises(TypeError):
            validate_input([1, 2, 3])


class TestFormatNumber:
    """Test cases for format_number function."""

    def test_format_integer(self):
        """Test formatting integers."""
        assert format_number(5) == "5.00"
        assert format_number(10, 0) == "10"
        assert format_number(42, 3) == "42.000"

    def test_format_float(self):
        """Test formatting floats."""
        assert format_number(3.14159, 2) == "3.14"
        assert format_number(3.14159, 4) == "3.1416"
        assert format_number(2.5, 1) == "2.5"

    def test_invalid_value_type(self):
        """Test that invalid value types raise TypeError."""
        with pytest.raises(TypeError):
            format_number("3.14")

    def test_invalid_decimal_places_type(self):
        """Test that invalid decimal_places types raise TypeError."""
        with pytest.raises(TypeError):
            format_number(3.14, "2")

    def test_negative_decimal_places(self):
        """Test that negative decimal_places raise ValueError."""
        with pytest.raises(ValueError):
            format_number(3.14, -1)


class TestIsEven:
    """Test cases for is_even function."""

    def test_even_numbers(self):
        """Test that even numbers return True."""
        assert is_even(2) is True
        assert is_even(4) is True
        assert is_even(0) is True
        assert is_even(-2) is True

    def test_odd_numbers(self):
        """Test that odd numbers return False."""
        assert is_even(1) is False
        assert is_even(3) is False
        assert is_even(-1) is False
        assert is_even(7) is False

    def test_invalid_input_type(self):
        """Test that non-integer inputs raise TypeError."""
        with pytest.raises(TypeError):
            is_even(2.5)
        
        with pytest.raises(TypeError):
            is_even("2")


class TestIsPrime:
    """Test cases for is_prime function."""

    def test_prime_numbers(self):
        """Test that prime numbers return True."""
        assert is_prime(2) is True
        assert is_prime(3) is True
        assert is_prime(5) is True
        assert is_prime(7) is True
        assert is_prime(11) is True
        assert is_prime(13) is True

    def test_non_prime_numbers(self):
        """Test that non-prime numbers return False."""
        assert is_prime(4) is False
        assert is_prime(6) is False
        assert is_prime(8) is False
        assert is_prime(9) is False
        assert is_prime(10) is False

    def test_edge_cases(self):
        """Test edge cases."""
        assert is_prime(0) is False
        assert is_prime(1) is False

    def test_invalid_input_type(self):
        """Test that non-integer inputs raise TypeError."""
        with pytest.raises(TypeError):
            is_prime(2.5)
        
        with pytest.raises(TypeError):
            is_prime("2")
