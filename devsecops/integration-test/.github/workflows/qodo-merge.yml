name: QODO Merge - PR Review and Auto Merge

on:
  pull_request:
    types: [opened, reopened, ready_for_review, synchronize]
    branches: [dev]  # Only trigger on PRs targeting dev branch
  pull_request_review:
    types: [submitted]
  issue_comment:
    types: [created, edited]

permissions:
  contents: write
  pull-requests: write
  issues: write
  checks: write
  actions: read

jobs:
  # Job 1: Run tests and code quality checks
  test-and-quality:
    if: github.event.pull_request.draft == false
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install Poetry
      uses: snok/install-poetry@v1
      with:
        version: latest
        virtualenvs-create: true
        virtualenvs-in-project: true

    - name: Load cached venv
      id: cached-poetry-dependencies
      uses: actions/cache@v3
      with:
        path: .venv
        key: venv-${{ runner.os }}-${{ steps.setup-python.outputs.python-version }}-${{ hashFiles('**/poetry.lock') }}

    - name: Install dependencies
      if: steps.cached-poetry-dependencies.outputs.cache-hit != 'true'
      run: poetry install --no-interaction --no-root

    - name: Install project
      run: poetry install --no-interaction

    - name: Run tests with coverage
      run: |
        poetry run pytest --cov=src/integration_test --cov-report=xml --cov-report=term-missing

    # - name: Run linting (flake8)
    #   run: |
    #     poetry run flake8 src tests

    - name: Run type checking (mypy)
      run: |
        poetry run mypy src

    # - name: Check code formatting (black)
    #   run: |
    #     poetry run black --check src tests

    # - name: Check import sorting (isort)
    #   run: |
    #     poetry run isort --check-only src tests

    # - name: Upload coverage to Codecov
    #   uses: codecov/codecov-action@v3
    #   with:
    #     file: ./coverage.xml
    #     flags: unittests
    #     name: codecov-umbrella

  # Job 2: QODO Merge PR Review
  qodo-merge-review:
    if: github.event.pull_request.draft == false
    runs-on: ubuntu-latest
    needs: test-and-quality
    
    steps:
    - name: QODO Merge PR Reviewer
      uses: Codium-ai/pr-agent@main
      env:
        OPENAI_KEY: ${{ secrets.OPENAI_KEY }}
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        command: /review
        pr_reviewer.num_code_suggestions: 6
        pr_reviewer.inline_code_comments: true
        pr_reviewer.require_tests_review: true
        pr_reviewer.require_security_review: true
        pr_reviewer.require_focused_review: true

  # Job 3: Auto-merge to main after successful review and approval
  auto-merge-to-main:
    if: |
      github.event.pull_request.draft == false &&
      contains(github.event.pull_request.labels.*.name, 'auto-merge') &&
      github.event.pull_request.base.ref == 'dev'
    runs-on: ubuntu-latest
    needs: [test-and-quality, qodo-merge-review]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
        token: ${{ secrets.GITHUB_TOKEN }}

    - name: Configure Git
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"

    - name: Check if PR is approved
      id: check-approval
      uses: actions/github-script@v7
      with:
        script: |
          const { data: reviews } = await github.rest.pulls.listReviews({
            owner: context.repo.owner,
            repo: context.repo.repo,
            pull_number: context.issue.number,
          });
          
          const approvedReviews = reviews.filter(review => review.state === 'APPROVED');
          const hasApproval = approvedReviews.length > 0;
          
          console.log(`PR has ${approvedReviews.length} approvals`);
          core.setOutput('approved', hasApproval);
          
          return hasApproval;

    - name: Merge PR to dev
      if: steps.check-approval.outputs.approved == 'true'
      run: |
        gh pr merge ${{ github.event.pull_request.number }} --squash --auto
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

    - name: Fast-forward main branch
      if: steps.check-approval.outputs.approved == 'true'
      run: |
        # Fetch latest dev after merge
        git fetch origin dev
        git checkout main
        git pull origin main
        
        # Fast-forward main to match dev
        git merge origin/dev --ff-only
        git push origin main

    - name: Comment on successful merge
      if: steps.check-approval.outputs.approved == 'true'
      uses: actions/github-script@v7
      with:
        script: |
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: '✅ PR has been successfully merged to `dev` and changes have been propagated to `main` branch!'
          });

  # Job 4: Handle QODO Merge commands in comments
  handle-qodo-commands:
    if: github.event.issue.pull_request && contains(github.event.comment.body, '/')
    runs-on: ubuntu-latest
    
    steps:
    - name: QODO Merge PR Agent Commands
      uses: Codium-ai/pr-agent@main
      env:
        OPENAI_KEY: ${{ secrets.OPENAI_KEY }}
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
