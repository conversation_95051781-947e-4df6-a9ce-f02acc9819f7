# Integration Test Project

A Python project with automated PR review and merging using QODO Merge.

## Features

- 🔄 Automated PR reviews using QODO Merge
- 🚀 Automatic merging to dev branch after review
- 🧪 Comprehensive testing with pytest
- 🎨 Code formatting with Black and isort
- 🔍 Type checking with mypy
- 📝 Code quality checks with flake8

## Project Structure

```
integration-test/
├── src/
│   └── integration_test/
│       ├── __init__.py
│       ├── main.py
│       └── utils.py
├── tests/
│   ├── __init__.py
│   └── test_main.py
├── .github/
│   └── workflows/
│       └── qodo-merge.yml
├── pyproject.toml
└── README.md
```

## Setup

1. Install Poetry if you haven't already:
   ```bash
   curl -sSL https://install.python-poetry.org | python3 -
   ```

2. Install dependencies:
   ```bash
   poetry install
   ```

3. Activate the virtual environment:
   ```bash
   poetry shell
   ```

## Development

### Running Tests
```bash
poetry run pytest
```

### Code Formatting
```bash
poetry run black src tests
poetry run isort src tests
```

### Type Checking
```bash
poetry run mypy src
```

### Linting
```bash
poetry run flake8 src tests
```

## GitHub Actions Workflow

This project includes a GitHub Actions workflow that:

1. **PR Review**: Automatically reviews pull requests using QODO Merge
2. **Code Quality**: Runs tests, linting, and type checking
3. **Auto Merge**: Merges approved PRs to the dev branch

### Required GitHub Setup

See [GITHUB_SETUP.md](GITHUB_SETUP.md) for detailed instructions on configuring your GitHub repository.

## Contributing

1. Create a feature branch from `main`
2. Make your changes
3. Create a pull request
4. QODO Merge will automatically review your PR
5. Once approved, the PR will be automatically merged to `dev`

## License

MIT License
