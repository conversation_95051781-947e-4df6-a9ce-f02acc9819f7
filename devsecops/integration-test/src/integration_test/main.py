"""Main module for the integration test package."""

from typing import Union

from .utils import validate_input


class Calculator:
    """A simple calculator class for demonstration purposes."""

    def add(self, a: Union[int, float], b: Union[int, float]) -> Union[int, float]:
        """Add two numbers.
        
        Args:
            a: First numbers
            b: Second numbers
            
        Returns:
            Sum of a and b
            
        Raises:
            TypeError: If inputs are not numbers
        """
        validate_input(a)
        validate_input(b)
        return a + b

    def subtract(self, a: Union[int, float], b: Union[int, float]) -> Union[int, float]:
        """Subtract two numbers.
        
        Args:
            a: First number
            b: Second number
            
        Returns:
            Difference of a and b
            
        Raises:
            TypeError: If inputs are not numbers
        """
        validate_input(a)
        validate_input(b)
        return a - b

    def multiply(self, a: Union[int, float], b: Union[int, float]) -> Union[int, float]:
        """Multiply two numbers.
        
        Args:
            a: First number
            b: Second number
            
        Returns:
            Product of a and b
            
        Raises:
            TypeError: If inputs are not numbers
        """
        validate_input(a)
        validate_input(b)
        return a * b

    def divide(self, a: Union[int, float], b: Union[int, float]) -> float:
        """Divide two numbers.
        
        Args:
            a: Dividend
            b: Divisor
            
        Returns:
            Quotient of a and b
            
        Raises:
            TypeError: If inputs are not numbers
            ZeroDivisionError: If divisor is zero
        """
        validate_input(a)
        validate_input(b)
        if b == 0:
            raise ZeroDivisionError("Cannot divide by zero")
        return a / b


def greet(name: str) -> str:
    """Generate a greeting message.
    
    Args:
        name: Name to greet
        
    Returns:
        Greeting message
        
    Raises:
        TypeError: If name is not a string
    """
    if not isinstance(name, str):
        raise TypeError("Name must be a string")
    return f"Hello, {name}!"


if __name__ == "__main__":
    calc = Calculator()
    print(f"2 + 3 = {calc.add(2, 3)}")
    print(f"10 - 4 = {calc.subtract(10, 4)}")
    print(f"5 * 6 = {calc.multiply(5, 6)}")
    print(f"15 / 3 = {calc.divide(15, 3)}")
    print(greet("World"))
