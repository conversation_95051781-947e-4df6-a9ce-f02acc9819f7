"""Utility functions for the integration test package."""

from typing import Union


def validate_input(value: Union[int, float]) -> None:
    """Validate that input is a number.
    
    Args:
        value: Value to validate
        
    Raises:
        TypeError: If value is not a number
    """
    if not isinstance(value, (int, float)):
        raise TypeError(f"Expected int or float, got {type(value).__name__}")


def format_number(value: Union[int, float], decimal_places: int = 2) -> str:
    """Format a number with specified decimal places.
    
    Args:
        value: Number to format
        decimal_places: Number of decimal places
        
    Returns:
        Formatted number as string
        
    Raises:
        TypeError: If value is not a number or decimal_places is not an int
        ValueError: If decimal_places is negative
    """
    validate_input(value)
    
    if not isinstance(decimal_places, int):
        raise TypeError("decimal_places must be an integer")
    
    if decimal_places < 0:
        raise ValueError("decimal_places must be non-negative")
    
    return f"{value:.{decimal_places}f}"


def is_even(number: int) -> bool:
    """Check if a number is even.
    
    Args:
        number: Integer to check
        
    Returns:
        True if number is even, False otherwise
        
    Raises:
        TypeError: If number is not an integer
    """
    if not isinstance(number, int):
        raise TypeError("Number must be an integer")
    
    return number % 2 == 0


def is_prime(number: int) -> bool:
    """Check if a number is prime.
    
    Args:
        number: Integer to check
        
    Returns:
        True if number is prime, False otherwise
        
    Raises:
        TypeError: If number is not an integer
        ValueError: If number is less than 2
    """
    if not isinstance(number, int):
        raise TypeError("Number must be an integer")
    
    if number < 2:
        return False
    
    if number == 2:
        return True
    
    if number % 2 == 0:
        return False
    
    for i in range(3, int(number**0.5) + 1, 2):
        if number % i == 0:
            return False
    
    return True
