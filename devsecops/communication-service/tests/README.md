# Communication Service Tests

This directory contains comprehensive unit tests for the optimized communication service, covering performance optimizations, error handling, and database operations.

## Test Structure

```
tests/
├── __init__.py                     # Test package initialization
├── conftest.py                     # Pytest configuration and fixtures
├── README.md                       # This file
├── services/                       # Service layer tests
│   ├── __init__.py
│   └── test_conversation_service.py # Conversation service tests
├── db/                            # Database layer tests
│   ├── __init__.py
│   └── test_mongo.py              # MongoDB connection and optimization tests
└── test_main.py                   # Main server tests
```

## Test Categories

### Unit Tests
- **Timeout Handler Tests**: Verify timeout decorators work correctly
- **Batch Loading Tests**: Test database query optimization
- **Error Handling Tests**: Ensure proper gRPC error responses
- **Service Method Tests**: Test all CRUD operations
- **Database Connection Tests**: Test connection pooling and optimizations
- **Server Configuration Tests**: Test gRPC server optimizations

### Performance Tests
- **Connection Pooling**: Verify optimal pool configurations
- **Query Optimization**: Test batch loading efficiency
- **Timeout Management**: Verify timeout configurations
- **Server Optimization**: Test gRPC server performance settings

## Running Tests

### Prerequisites
```bash
# Install test dependencies using Poetry
poetry install --with test

# Or install all dependencies including dev and test
poetry install --with dev,test
```

### Using the Test Runner Script
```bash
# Install dependencies first
python run_tests.py --install-deps

# Run all tests
python run_tests.py --all

# Run unit tests only
python run_tests.py --unit

# Run with coverage report
python run_tests.py --all --coverage

# Run code quality checks
python run_tests.py --quality

# Run specific test file
python run_tests.py --test tests/services/test_conversation_service.py

# Verbose output
python run_tests.py --all --verbose
```

### Using Poetry and Pytest Directly
```bash
# Run all tests
poetry run pytest tests/

# Run unit tests only
poetry run pytest tests/ -m unit

# Run with coverage
poetry run pytest tests/ --cov=app --cov-report=html --cov-report=term

# Run specific test file
poetry run pytest tests/services/test_conversation_service.py

# Run specific test function
poetry run pytest tests/services/test_conversation_service.py::TestTimeoutHandler::test_timeout_handler_success
```

## Test Coverage

The tests cover the following optimizations:

### Conversation Service Optimizations
- ✅ Timeout decorators (10-15 seconds)
- ✅ Error handling with proper gRPC status codes
- ✅ Batch loading for task queries
- ✅ Database query optimization
- ✅ Authorization checks

### Database Optimizations
- ✅ Connection pooling (50 max, 5 min connections)
- ✅ Timeout configurations
- ✅ MongoDB Atlas and standard MongoDB support
- ✅ Index creation for performance
- ✅ Retry logic and error handling

### Server Optimizations
- ✅ Thread pool configuration (50 workers)
- ✅ gRPC keepalive settings
- ✅ Message size limits
- ✅ Graceful shutdown handling
- ✅ Health monitoring setup

## Test Fixtures

The `conftest.py` file provides common fixtures:

- `mock_grpc_context`: Mock gRPC servicer context
- `mock_conversation_request`: Mock conversation request
- `mock_conversation_model`: Mock conversation model
- `mock_database_connection`: Mock database connection
- `mock_logger`: Mock logger
- `mock_environment_variables`: Mock environment variables
- `mock_settings`: Mock application settings

## Performance Benchmarks

The tests include performance benchmarks for:

- Database connection establishment time
- Query execution time with and without optimizations
- Batch loading vs individual queries
- Server startup time
- Memory usage with connection pooling

## Continuous Integration

These tests are designed to run in CI/CD pipelines with:

- Automated test execution
- Coverage reporting
- Code quality checks
- Performance regression detection

## Troubleshooting

### Common Issues

1. **Import Errors**: Ensure the app directory is in Python path
2. **Database Connection**: Tests use mocked connections by default
3. **gRPC Dependencies**: Install grpcio-testing for gRPC-specific tests
4. **Coverage Reports**: HTML reports are generated in `htmlcov/` directory

### Environment Variables

Tests use mocked environment variables by default. For integration tests, set:

```bash
export MONGO_HOST=localhost
export MONGO_PORT=27017
export MONGO_DB_NAME=test_communication_service
export GRPC_PORT=50055
```

## Contributing

When adding new tests:

1. Follow the existing test structure
2. Use appropriate fixtures from `conftest.py`
3. Add performance tests for optimization features
4. Ensure proper mocking of external dependencies
5. Update this README if adding new test categories