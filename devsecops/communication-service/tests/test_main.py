"""
Unit tests for the main server module with optimizations.

This module tests the gRPC server configuration, graceful shutdown,
and performance optimizations.
"""

import unittest
from unittest.mock import Mock, patch, MagicMock
import signal
import sys
from concurrent import futures

from app.main import serve, cleanup, signal_handler


class TestMainServer(unittest.TestCase):
    """Test cases for the main server functionality."""

    @patch('app.main.server', None)
    @patch('app.main.cleanup')
    @patch('sys.exit')
    def test_signal_handler_with_server(self, mock_exit, mock_cleanup):
        """Test signal handler when server is running."""
        # Mock global server
        mock_server = Mock()
        
        with patch('app.main.server', mock_server):
            signal_handler(signal.SIGTERM, None)
            
            # Verify graceful shutdown was called
            mock_server.stop.assert_called_once_with(grace=30)
            mock_cleanup.assert_called_once()
            mock_exit.assert_called_once_with(0)

    @patch('app.main.server', None)
    @patch('app.main.cleanup')
    @patch('sys.exit')
    def test_signal_handler_without_server(self, mock_exit, mock_cleanup):
        """Test signal handler when no server is running."""
        signal_handler(signal.SIGINT, None)
        
        # Verify cleanup and exit were called
        mock_cleanup.assert_called_once()
        mock_exit.assert_called_once_with(0)

    @patch('app.main.close_db')
    def test_cleanup_success(self, mock_close_db):
        """Test successful cleanup operation."""
        cleanup()
        mock_close_db.assert_called_once()

    @patch('app.main.close_db')
    def test_cleanup_error(self, mock_close_db):
        """Test cleanup with database error."""
        mock_close_db.side_effect = Exception("Database error")
        
        with self.assertRaises(RuntimeError) as context:
            cleanup()
        
        self.assertIn("Error during cleanup", str(context.exception))

    @patch('app.main.init_mongo_from_env')
    @patch('app.main.create_indexes')
    @patch('app.main.grpc.server')
    @patch('app.main.health.HealthServicer')
    @patch('app.main.CommunicationService')
    @patch('app.main.signal.signal')
    @patch('app.main.settings')
    def test_serve_success(self, mock_settings, mock_signal, mock_comm_service,
                          mock_health_servicer, mock_grpc_server, 
                          mock_create_indexes, mock_init_mongo):
        """Test successful server startup with optimizations."""
        # Mock settings
        mock_settings.GRPC_PORT = "50055"
        
        # Mock server
        mock_server = Mock()
        mock_grpc_server.return_value = mock_server
        
        # Mock health servicer
        mock_health = Mock()
        mock_health_servicer.return_value = mock_health
        
        # Mock communication service
        mock_service = Mock()
        mock_comm_service.return_value = mock_service
        
        # Mock server termination (to avoid infinite wait)
        mock_server.wait_for_termination.side_effect = KeyboardInterrupt()
        
        try:
            serve()
        except KeyboardInterrupt:
            pass  # Expected to exit the serve function
        
        # Verify MongoDB initialization
        mock_init_mongo.assert_called_once()
        mock_create_indexes.assert_called_once()
        
        # Verify server creation with optimizations
        mock_grpc_server.assert_called_once()
        call_args = mock_grpc_server.call_args
        
        # Check thread pool executor
        self.assertIsInstance(call_args[0][0], futures.ThreadPoolExecutor)
        
        # Check optimization options
        options = call_args[1]['options']
        option_dict = {opt[0]: opt[1] for opt in options}
        
        self.assertEqual(option_dict['grpc.keepalive_time_ms'], 30000)
        self.assertEqual(option_dict['grpc.keepalive_timeout_ms'], 5000)
        self.assertTrue(option_dict['grpc.keepalive_permit_without_calls'])
        self.assertEqual(option_dict['grpc.max_receive_message_length'], 4 * 1024 * 1024)
        self.assertEqual(option_dict['grpc.max_send_message_length'], 4 * 1024 * 1024)
        
        # Verify health servicer setup
        mock_health_servicer.assert_called_once_with(
            experimental_non_blocking=True,
            experimental_thread_pool=unittest.mock.ANY
        )
        
        # Verify signal handlers were registered
        signal_calls = mock_signal.call_args_list
        signal_types = [call[0][0] for call in signal_calls]
        self.assertIn(signal.SIGINT, signal_types)
        self.assertIn(signal.SIGTERM, signal_types)
        
        # Verify server was started
        mock_server.start.assert_called_once()
        mock_server.add_insecure_port.assert_called_once_with("[::]:50055")

    @patch('app.main.init_mongo_from_env')
    @patch('app.main.cleanup')
    def test_serve_mongo_error(self, mock_cleanup, mock_init_mongo):
        """Test server startup with MongoDB initialization error."""
        mock_init_mongo.side_effect = Exception("MongoDB connection failed")
        
        with self.assertRaises(Exception):
            serve()
        
        # Verify cleanup was called in finally block
        mock_cleanup.assert_called_once()

    @patch('app.main.init_mongo_from_env')
    @patch('app.main.create_indexes')
    @patch('app.main.grpc.server')
    @patch('app.main.cleanup')
    def test_serve_server_creation_error(self, mock_cleanup, mock_grpc_server,
                                        mock_create_indexes, mock_init_mongo):
        """Test server startup with gRPC server creation error."""
        mock_grpc_server.side_effect = Exception("Server creation failed")
        
        with self.assertRaises(Exception):
            serve()
        
        # Verify cleanup was called
        mock_cleanup.assert_called_once()


class TestServerOptimizations(unittest.TestCase):
    """Test cases for server optimization configurations."""

    def test_thread_pool_configuration(self):
        """Test thread pool executor configuration."""
        with patch('app.main.futures.ThreadPoolExecutor') as mock_executor:
            with patch('app.main.grpc.server') as mock_server:
                mock_server.return_value = Mock()
                
                try:
                    with patch('app.main.init_mongo_from_env'), \
                         patch('app.main.create_indexes'), \
                         patch('app.main.settings.GRPC_PORT', "50055"), \
                         patch('app.main.signal.signal'):
                        
                        # Mock server to avoid infinite wait
                        mock_server.return_value.wait_for_termination.side_effect = KeyboardInterrupt()
                        
                        serve()
                except KeyboardInterrupt:
                    pass
                
                # Verify ThreadPoolExecutor was called with max_workers=50
                executor_calls = mock_executor.call_args_list
                main_executor_call = executor_calls[0]  # First call is for main server
                self.assertEqual(main_executor_call[1]['max_workers'], 50)

    def test_grpc_options_configuration(self):
        """Test gRPC server options are properly configured."""
        expected_options = [
            ('grpc.keepalive_time_ms', 30000),
            ('grpc.keepalive_timeout_ms', 5000),
            ('grpc.keepalive_permit_without_calls', True),
            ('grpc.http2.max_pings_without_data', 0),
            ('grpc.http2.min_time_between_pings_ms', 10000),
            ('grpc.http2.min_ping_interval_without_data_ms', 300000),
            ('grpc.max_receive_message_length', 4 * 1024 * 1024),
            ('grpc.max_send_message_length', 4 * 1024 * 1024),
        ]
        
        with patch('app.main.grpc.server') as mock_server:
            mock_server.return_value = Mock()
            
            try:
                with patch('app.main.init_mongo_from_env'), \
                     patch('app.main.create_indexes'), \
                     patch('app.main.settings.GRPC_PORT', "50055"), \
                     patch('app.main.signal.signal'):
                    
                    mock_server.return_value.wait_for_termination.side_effect = KeyboardInterrupt()
                    serve()
            except KeyboardInterrupt:
                pass
            
            # Verify server was created with correct options
            call_args = mock_server.call_args
            actual_options = call_args[1]['options']
            
            for expected_option in expected_options:
                self.assertIn(expected_option, actual_options)

    def test_health_servicer_optimization(self):
        """Test health servicer is configured with optimizations."""
        with patch('app.main.health.HealthServicer') as mock_health_servicer:
            with patch('app.main.grpc.server') as mock_server:
                mock_server.return_value = Mock()
                
                try:
                    with patch('app.main.init_mongo_from_env'), \
                         patch('app.main.create_indexes'), \
                         patch('app.main.settings.GRPC_PORT', "50055"), \
                         patch('app.main.signal.signal'):
                        
                        mock_server.return_value.wait_for_termination.side_effect = KeyboardInterrupt()
                        serve()
                except KeyboardInterrupt:
                    pass
                
                # Verify health servicer configuration
                call_args = mock_health_servicer.call_args
                self.assertTrue(call_args[1]['experimental_non_blocking'])
                self.assertIsInstance(call_args[1]['experimental_thread_pool'], 
                                    futures.ThreadPoolExecutor)


class TestGracefulShutdown(unittest.TestCase):
    """Test cases for graceful shutdown functionality."""

    def test_signal_registration(self):
        """Test that signal handlers are properly registered."""
        with patch('app.main.signal.signal') as mock_signal:
            with patch('app.main.grpc.server') as mock_server:
                mock_server.return_value = Mock()
                
                try:
                    with patch('app.main.init_mongo_from_env'), \
                         patch('app.main.create_indexes'), \
                         patch('app.main.settings.GRPC_PORT', "50055"):
                        
                        mock_server.return_value.wait_for_termination.side_effect = KeyboardInterrupt()
                        serve()
                except KeyboardInterrupt:
                    pass
                
                # Verify signal handlers were registered
                signal_calls = mock_signal.call_args_list
                registered_signals = [call[0][0] for call in signal_calls]
                
                self.assertIn(signal.SIGINT, registered_signals)
                self.assertIn(signal.SIGTERM, registered_signals)

    def test_graceful_shutdown_timeout(self):
        """Test graceful shutdown timeout configuration."""
        mock_server = Mock()
        
        with patch('app.main.server', mock_server):
            with patch('app.main.cleanup'), \
                 patch('sys.exit'):
                
                signal_handler(signal.SIGTERM, None)
                
                # Verify server.stop was called with 30 second grace period
                mock_server.stop.assert_called_once_with(grace=30)


if __name__ == '__main__':
    unittest.main()