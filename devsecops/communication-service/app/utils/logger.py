"""
Logging utility module for the communication service.
Provides a configured colorlog logger that can be used across the application.
"""

# Standard library imports
import logging
import sys
from typing import Any, Dict, Optional

# Third-party imports
import colorlog


# Structured logger class
class StructuredLogger(logging.Logger):
    """
    Custom logger class that supports structured logging with extra parameters.
    """

    # Override the _log method
    def _log(
        self,
        level: int,
        msg: str,
        args: tuple,
        exc_info: Optional[Exception] = None,
        extra: Optional[Dict[str, Any]] = None,
        stack_info: bool = False,
        stacklevel: int = 1,
        **kwargs: Any,
    ) -> None:
        """
        Override the _log method to support keyword arguments in log messages.

        Args:
            level: The logging level
            msg: The log message
            args: Arguments for message formatting
            exc_info: Exception information
            extra: Extra contextual information
            stack_info: Include stack information
            stacklevel: Number of frames to skip for caller information
            **kwargs: Additional keyword arguments for structured logging
        """

        # Update the extra parameters
        if kwargs:
            # Update the extra parameters
            extra = extra or {}
            extra.update(kwargs)

            # Format the message with the extra parameters
            structured_msg = f"{msg} | " + " | ".join(f"{k}={v}" for k, v in sorted(kwargs.items()))
        else:
            # Format the message
            structured_msg = msg

        # Log the message
        super()._log(level, structured_msg, args, exc_info, extra, stack_info, stacklevel)


# Setup logger function
def setup_logger(name: str = None, log_level: str = "INFO") -> logging.Logger:
    """
    Set up a colored logger with the specified name and log level.

    Args:
        name (str, optional): Logger name. If None, returns the root logger.
        log_level (str, optional): Logging level. Defaults to "INFO".

    Returns:
        logging.Logger: Configured logger instance
    """
    # Register the custom logger class
    logging.setLoggerClass(StructuredLogger)

    # Get the logger
    logger = colorlog.getLogger(name)

    # Only add handler if the logger doesn't have handlers
    if not logger.handlers:
        # Create console handler
        handler = colorlog.StreamHandler(sys.stdout)

        # Create formatter
        formatter = colorlog.ColoredFormatter(
            "%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            log_colors={
                "DEBUG": "cyan",
                "INFO": "green",
                "WARNING": "yellow",
                "ERROR": "red",
                "CRITICAL": "red,bg_white",
            },
            reset=True,
            style="%",
            datefmt="%Y-%m-%d %H:%M:%S",
        )

        # Add formatter to handler
        handler.setFormatter(formatter)

        # Add handler to logger
        logger.addHandler(handler)

    # Set log level
    logger.setLevel(getattr(logging, log_level.upper()))

    # Prevent propagation to avoid duplicate logs
    logger.propagate = False

    # Return the logger
    return logger


# Create default logger
logger = setup_logger("communication-service")
