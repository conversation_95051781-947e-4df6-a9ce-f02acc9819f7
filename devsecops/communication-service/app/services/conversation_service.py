"""
Service implementation for handling conversation-related operations.
"""

# Standard library imports
import asyncio
from functools import wraps
from typing import List, Optional, Dict, Any

# Third-party imports
import grpc
from dotenv import load_dotenv
from google.protobuf import empty_pb2
from pymongo.errors import PyMongoError

# Local imports
from app.grpc_ import communication_pb2, communication_pb2_grpc
from app.models.conversation_model import Conversation
from app.models.message_model import Message
from app.models.task_model import Task
from app.utils.logger import setup_logger

# Load environment variables from .env file
load_dotenv()

# Initialize logger
logger = setup_logger("communication-service/services/conversation_service.py")


def timeout_handler(timeout_seconds: int = 10):
    """
    Decorator to handle operation timeouts and provide consistent error handling.
    Thread-safe implementation using concurrent.futures for gRPC worker threads.

    Args:
        timeout_seconds: Maximum time to wait for operation completion
    """

    def decorator(func):
        @wraps(func)
        def wrapper(self, request, context):
            try:
                # Use concurrent.futures for thread-safe timeout handling
                import concurrent.futures
                import threading

                # Execute the function with timeout in a thread-safe way
                with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                    future = executor.submit(func, self, request, context)
                    try:
                        result = future.result(timeout=timeout_seconds)
                        return result
                    except concurrent.futures.TimeoutError:
                        raise TimeoutError(f"Operation timed out after {timeout_seconds} seconds")

            except TimeoutError as e:
                logger.error(f"Operation timeout in {func.__name__}", error=str(e))
                context.abort(grpc.StatusCode.DEADLINE_EXCEEDED, str(e))
            except PyMongoError as e:
                logger.error(f"Database error in {func.__name__}", error=str(e))
                context.abort(grpc.StatusCode.INTERNAL, f"Database error: {str(e)}")
            except Exception as e:
                logger.error(f"Unexpected error in {func.__name__}", error=str(e))
                context.abort(grpc.StatusCode.INTERNAL, f"Internal error: {str(e)}")

        return wrapper

    return decorator


def batch_load_tasks(conversation_ids: List[str]) -> Dict[str, List]:
    """
    Efficiently load tasks for multiple conversations in a single query.

    Args:
        conversation_ids: List of conversation IDs to load tasks for

    Returns:
        Dictionary mapping conversation ID to list of tasks
    """
    if not conversation_ids:
        return {}

    try:
        # Single query to get all tasks for all conversations
        tasks = Task.objects.filter(globalChatConversationId__in=conversation_ids)

        # Group tasks by conversation ID
        tasks_by_conversation = {}
        for task in tasks:
            conv_id = str(task.globalChatConversationId)
            if conv_id not in tasks_by_conversation:
                tasks_by_conversation[conv_id] = []
            tasks_by_conversation[conv_id].append(task)

        return tasks_by_conversation
    except Exception as e:
        logger.error("Failed to batch load tasks", error=str(e))
        return {}


# Conversation service class
class ConversationService(communication_pb2_grpc.CommunicationServiceServicer):
    """
    Service class for handling conversation-related operations.
    Implements CRUD operations for conversations.
    """

    # Create conversation
    @timeout_handler(timeout_seconds=15)
    def createConversation(
        self,
        request: communication_pb2.CreateConversationRequest,
        context: grpc.ServicerContext,
    ) -> communication_pb2.Conversation:
        """
        Create a new conversation or return existing one based on chat type specific rules.

        For CHAT_TYPE_GLOBAL: Returns existing if no messages AND no tasks
        For CHAT_TYPE_AGENT: Returns existing if no messages (tasks are ignored)

        Args:
            request: CreateConversationRequest containing conversation details
            context: gRPC servicer context

        Returns:
            Created conversation or existing conversation based on chat type rules
        """
        try:
            # Log request details
            logger.info(
                "Creating new conversation or checking existing ones",
                userId=request.userId,
                agentId=request.agentId if request.agentId else None,
                chatType=request.chatType,
            )

            # Convert int chatType to string enum name for filtering
            chat_type_name = communication_pb2.ChatType.Name(request.chatType)

            # Build filter parameters for existing conversations
            filter_params = {
                "userId": request.userId,
                "chatType": chat_type_name,
            }

            # Add agentId to filter if provided
            if request.agentId:
                filter_params["agentId"] = request.agentId

            # Check for existing conversations with the same criteria - get most recent directly
            most_recent_conversation = (
                Conversation.objects.filter(**filter_params).order_by("-createdAt").first()
            )

            if most_recent_conversation:
                logger.info(
                    "Found existing conversation, checking based on chat type",
                    conversationId=str(most_recent_conversation.id),
                    chatType=chat_type_name,
                )

                # Optimized: Use aggregation to get counts in single query
                from mongoengine import Q

                # Check if the most recent conversation has any messages
                message_count = Message.objects.filter(
                    conversationId=most_recent_conversation.id
                ).count()

                # Determine if we should return existing conversation based on chat type
                should_return_existing = False
                task_count = 0

                if chat_type_name == "CHAT_TYPE_GLOBAL":
                    # For GLOBAL chat: check both messages and tasks
                    task_count = Task.objects.filter(
                        globalChatConversationId=most_recent_conversation.id
                    ).count()

                    should_return_existing = message_count == 0 and task_count == 0

                    if should_return_existing:
                        logger.info(
                            "Global conversation has no messages and no tasks, returning existing",
                            conversationId=str(most_recent_conversation.id),
                            messageCount=message_count,
                            taskCount=task_count,
                        )
                    else:
                        logger.info(
                            "Global conversation has messages or tasks, creating new",
                            conversationId=str(most_recent_conversation.id),
                            messageCount=message_count,
                            taskCount=task_count,
                        )

                elif chat_type_name == "CHAT_TYPE_AGENT":
                    # For AGENT chat: check only messages (ignore tasks)
                    should_return_existing = message_count == 0

                    if should_return_existing:
                        logger.info(
                            "Agent conversation has no messages, returning existing",
                            conversationId=str(most_recent_conversation.id),
                            messageCount=message_count,
                        )
                    else:
                        logger.info(
                            "Agent conversation has messages, creating new",
                            conversationId=str(most_recent_conversation.id),
                            messageCount=message_count,
                        )

                # Return existing conversation if criteria are met
                if should_return_existing:
                    # Optimized: Only fetch tasks if needed for GLOBAL chat
                    if chat_type_name == "CHAT_TYPE_GLOBAL":
                        tasks = Task.objects.filter(
                            globalChatConversationId=most_recent_conversation.id
                        )
                    else:
                        tasks = []
                    return most_recent_conversation.to_proto(tasks=tasks)

            # Either no existing conversations or criteria not met, create new one
            conversation = Conversation(
                userId=request.userId,
                agentId=request.agentId if request.agentId else None,
                chatType=chat_type_name,
            )
            conversation.save()

            # Log success
            logger.info("Conversation created successfully", conversationId=conversation.id)

            # Get tasks for the new conversation (will be empty for new conversations)
            tasks = Task.objects.filter(globalChatConversationId=conversation.id)

            # Convert to protobuf message
            return conversation.to_proto(tasks=tasks)

        except Exception as e:
            # Log error
            logger.error("Failed to create conversation", error=str(e))

            # Raise gRPC error
            context.abort(grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}")

    # Get conversation
    @timeout_handler(timeout_seconds=10)
    def getConversation(
        self,
        request: communication_pb2.GetConversationRequest,
        context: grpc.ServicerContext,
    ) -> communication_pb2.Conversation:
        """
        Get conversation by ID.

        Args:
            request: GetConversationRequest containing conversation ID and user ID
            context: gRPC servicer context

        Returns:
            Conversation with the specified ID if the user is authorized
        """
        try:
            # Log request details
            logger.info(
                "Getting conversation",
                conversationId=request.conversationId,
                userId=request.userId,
            )

            # Get conversation from database
            conversation = Conversation.objects.get(id=request.conversationId)

            # Check if the user is authorized to access this conversation
            if conversation.userId != request.userId:
                logger.warning(
                    "Unauthorized access attempt",
                    conversationId=request.conversationId,
                    requestUserId=request.userId,
                    conversationUserId=conversation.userId,
                )
                context.abort(
                    grpc.StatusCode.PERMISSION_DENIED,
                    f"User {request.userId} is not authorized to access conversation {request.conversationId}",
                )

            # Log success
            logger.info("Conversation retrieved successfully", conversationId=conversation.id)

            # Get tasks associated with this conversation
            tasks = Task.objects.filter(globalChatConversationId=conversation.id)

            # Convert to protobuf message with tasks
            return conversation.to_proto(tasks=tasks)

        except Exception as e:
            # Log error
            logger.error("Failed to get conversation", error=str(e))

            # Raise gRPC error
            context.abort(grpc.StatusCode.INTERNAL, f"Failed to get conversation: {str(e)}")

    # Update conversation tokens
    @timeout_handler(timeout_seconds=10)
    def updateConversationTokens(
        self,
        request: communication_pb2.UpdateConversationTokensRequest,
        context: grpc.ServicerContext,
    ) -> empty_pb2.Empty:
        """
        Update conversation tokens.

        Args:
            request: UpdateConversationTokensRequest containing conversation ID and user ID
            context: gRPC servicer context

        Returns:
            Empty response
        """
        try:
            # Log request details
            logger.info(
                "Updating conversation tokens",
                conversationId=request.conversationId,
                inputTokens=request.inputTokens,
                outputTokens=request.outputTokens,
                userId=request.userId,
            )

            # Get conversation from database
            conversation = Conversation.objects.get(id=request.conversationId)

            # Check if the user is authorized to update tokens for this conversation
            if conversation.userId != request.userId:
                logger.warning(
                    "Unauthorized token update attempt",
                    conversationId=request.conversationId,
                    requestUserId=request.userId,
                    conversationUserId=conversation.userId,
                )
                context.abort(
                    grpc.StatusCode.PERMISSION_DENIED,
                    f"User {request.userId} is not authorized to update tokens for conversation {request.conversationId}",
                )

            # Update conversation tokens by adding new tokens to existing ones
            old_input_tokens = conversation.inputTokens or 0
            old_output_tokens = conversation.outputTokens or 0

            new_input_tokens = request.inputTokens if request.inputTokens else 0
            new_output_tokens = request.outputTokens if request.outputTokens else 0

            conversation.inputTokens = old_input_tokens + new_input_tokens
            conversation.outputTokens = old_output_tokens + new_output_tokens
            conversation.save()

            # Log success with detailed token information
            logger.info(
                "Conversation tokens updated successfully",
                conversationId=request.conversationId,
                oldInputTokens=old_input_tokens,
                newInputTokens=new_input_tokens,
                totalInputTokens=conversation.inputTokens,
                oldOutputTokens=old_output_tokens,
                newOutputTokens=new_output_tokens,
                totalOutputTokens=conversation.outputTokens,
            )

            # Return empty response
            return empty_pb2.Empty()

        except Exception as e:
            # Log error
            logger.error("Failed to update conversation tokens", error=str(e))

            # Raise gRPC error
            context.abort(
                grpc.StatusCode.INTERNAL,
                f"Failed to update conversation tokens: {str(e)}",
            )

    # Delete conversation
    @timeout_handler(timeout_seconds=10)
    def deleteConversation(
        self,
        request: communication_pb2.DeleteConversationRequest,
        context: grpc.ServicerContext,
    ) -> empty_pb2.Empty:
        """
        Delete conversation by ID.

        Args:
            request: DeleteConversationRequest containing conversation ID and user ID
            context: gRPC servicer context

        Returns:
            Empty response if the user is authorized
        """
        try:
            # Log request details
            logger.info(
                "Deleting conversation",
                conversationId=request.conversationId,
                userId=request.userId,
            )

            # Get conversation from database
            conversation = Conversation.objects.get(id=request.conversationId)

            # Check if the user is authorized to delete this conversation
            if conversation.userId != request.userId:
                logger.warning(
                    "Unauthorized deletion attempt",
                    conversationId=request.conversationId,
                    requestUserId=request.userId,
                    conversationUserId=conversation.userId,
                )
                context.abort(
                    grpc.StatusCode.PERMISSION_DENIED,
                    f"User {request.userId} is not authorized to delete conversation {request.conversationId}",
                )

            # Delete conversation from database
            conversation.delete()

            # Log success
            logger.info(
                "Conversation deleted successfully",
                conversationId=request.conversationId,
            )

            # Return empty response
            return empty_pb2.Empty()

        except Exception as e:
            # Log error
            logger.error("Failed to delete conversation", error=str(e))

            # Raise gRPC error
            context.abort(grpc.StatusCode.INTERNAL, f"Failed to delete conversation: {str(e)}")

    # List conversations
    @timeout_handler(timeout_seconds=15)
    def listConversations(
        self,
        request: communication_pb2.ListConversationsRequest,
        context: grpc.ServicerContext,
    ) -> communication_pb2.ListConversationsResponse:
        """
        List conversations with enhanced filtering options.

        Supports 4 filtering modes:
        1. All conversations (no filters) - returns all user conversations ordered by latest first
        2. By chatType - filters by specific chat type (CHAT_TYPE_GLOBAL, CHAT_TYPE_AGENT)
        3. By agentId - filters by specific agent conversations
        4. By search query - searches in conversation titles using regex

        Args:
            request: ListConversationsRequest containing query parameters
            context: gRPC servicer context

        Returns:
            List of conversations matching the filters
        """
        try:
            # Log request details
            logger.info(
                "Listing conversations with filters",
                userId=request.userId,
                chatType=request.chatType,
                agentId=request.agentId if request.agentId else None,
                search=getattr(request, "search", None) if hasattr(request, "search") else None,
            )

            # Pagination parameters with robust type checking
            try:
                page_size = int(request.limit)
                if page_size <= 0:
                    page_size = 10
            except (TypeError, ValueError):
                page_size = 10

            try:
                page = int(request.page)
                if page <= 0:
                    page = 1
            except (TypeError, ValueError):
                page = 1

            skip = (page - 1) * page_size

            # Start building the filter with userId (mandatory)
            filter_params = {"userId": request.userId}

            # Initialize MongoDB query for advanced filtering
            from mongoengine import Q

            query = Q(userId=request.userId)

            # Filter 1: All conversations (default) - no additional filters needed
            # Just the userId filter will be applied

            # Filter 2: By chatType - add chatType filter if specified (not UNSPECIFIED)
            if request.chatType != 0:
                chat_type_name = communication_pb2.ChatType.Name(request.chatType)
                filter_params["chatType"] = chat_type_name
                query = query & Q(chatType=chat_type_name)
                logger.info("Applying chatType filter", chatType=chat_type_name)

            # Filter 3: By agentId - add agentId filter if provided
            if request.agentId:
                filter_params["agentId"] = request.agentId
                query = query & Q(agentId=request.agentId)
                logger.info("Applying agentId filter", agentId=request.agentId)

            # Filter 4: By search query - search in conversation titles using regex
            search_query = getattr(request, "search", None) if hasattr(request, "search") else None
            if search_query and search_query.strip():
                # Use MongoDB regex for case-insensitive search in title field
                # Escape special regex characters to prevent injection
                import re

                escaped_search = re.escape(search_query.strip())
                # Create case-insensitive regex pattern
                title_regex = f".*{escaped_search}.*"
                query = query & Q(title__iregex=title_regex)
                logger.info("Applying search filter", searchQuery=search_query)

            # Apply the query and order by creation date (latest first)
            # CRITICAL OPTIMIZATION: Only select necessary fields for list view
            conversation_filter = Conversation.objects(query).only(
                'id', 'userId', 'agentId', 'chatType', 'title',
                'inputTokens', 'outputTokens', 'createdAt', 'updatedAt'
            ).order_by("-updatedAt")

            # Optimize: Get paginated results first, then count only if needed
            conversations = list(conversation_filter[skip:skip + page_size])
            
            # Only count if we need pagination metadata (avoid expensive count query)
            if page == 1 and len(conversations) < page_size:
                # If first page and less than page_size results, we have all data
                total_count = len(conversations)
                total_pages = 1
            else:
                # Only do expensive count when necessary
                total_count = conversation_filter.count()
                total_pages = (total_count + page_size - 1) // page_size

            # Log success with filter summary
            logger.info(
                "Conversations listed successfully with filters applied",
                userId=request.userId,
                chatType=request.chatType if request.chatType != 0 else "ALL",
                agentId=request.agentId if request.agentId else "ALL",
                search=search_query if search_query else "NONE",
                totalFound=total_count,
                pageSize=page_size,
                currentPage=page,
            )

            # Build pagination metadata
            metadata = communication_pb2.PaginationMetadata(
                total=total_count,
                totalPages=total_pages,
                currentPage=page,
                pageSize=page_size,
                hasNextPage=page < total_pages,
                hasPreviousPage=page > 1,
            )

            # Optimize: Only load tasks if conversations have chatType that needs them
            # Most list operations don't need full task details
            conversation_protos = []
            
            # Check if any conversations need task loading
            needs_tasks = any(conv.chatType == "CHAT_TYPE_GLOBAL" for conv in conversations)
            
            if needs_tasks and len(conversations) <= 20:  # Only batch load for reasonable sizes
                # Use batch loading for better performance when needed
                conversation_ids = [str(conv.id) for conv in conversations]
                tasks_by_conversation = batch_load_tasks(conversation_ids)
                
                for conversation in conversations:
                    conv_id = str(conversation.id)
                    tasks = tasks_by_conversation.get(conv_id, [])
                    conversation_proto = conversation.to_proto(tasks=tasks)
                    conversation_protos.append(conversation_proto)
            else:
                # Fast path: Convert without task loading for better performance
                for conversation in conversations:
                    # Pass empty tasks list for faster conversion
                    conversation_proto = conversation.to_proto(tasks=[])
                    conversation_protos.append(conversation_proto)

            # Return response with conversations including their tasks
            return communication_pb2.ListConversationsResponse(
                data=conversation_protos,
                metadata=metadata,
            )

        except Exception as e:
            # Log error with filter context
            logger.error(
                "Failed to list conversations",
                error=str(e),
                userId=request.userId,
                chatType=request.chatType,
                agentId=request.agentId if request.agentId else None,
                search=getattr(request, "search", None) if hasattr(request, "search") else None,
            )

            # Raise gRPC error
            context.abort(grpc.StatusCode.INTERNAL, f"Failed to list conversations: {str(e)}")
