"""
Service implementation for handling message-related operations.
"""

# Standard libraries
import grpc
from google.protobuf import any_pb2, empty_pb2

# Local imports
from app.grpc_ import communication_pb2, communication_pb2_grpc
from app.models.conversation_model import Conversation
from app.models.message_model import Message
from app.utils.logger import setup_logger

# Initialize logger
logger = setup_logger("communication-service/services/message_service.py")


# Message service class
class MessageService(communication_pb2_grpc.CommunicationServiceServicer):
    """
    Service class for handling message-related operations.
    Implements CRUD operations for messages with JSON data support.
    """

    # Create message
    def createMessage(
        self,
        request: communication_pb2.CreateMessageRequest,
        context: grpc.ServicerContext,
    ) -> communication_pb2.Message:
        """
        Create a new message with JSON data support.

        Args:
            request: CreateMessageRequest containing message details and user ID
            context: gRPC servicer context

        Returns:
            Created message if the user is authorized
        """
        try:
            # Log request details
            logger.info(
                "Creating new message",
                conversationId=request.conversationId,
                senderType=request.senderType,
                hasData=bool(request.HasField("data")),
                workflowId=request.workflowId,
                workflowResponse=len(request.workflowResponse),
                status=request.status if request.HasField("status") else None,
                type=request.type,
            )

            # Check if the user is authorized to create a message in this conversation
            try:
                conversation = Conversation.objects.get(id=request.conversationId)
                if conversation.userId != request.userId:
                    logger.warning(
                        "Unauthorized message creation attempt",
                        conversationId=request.conversationId,
                        requestUserId=request.userId,
                        conversationUserId=conversation.userId,
                    )
                    context.abort(
                        grpc.StatusCode.PERMISSION_DENIED,
                        f"User {request.userId} is not authorized to create messages in conversation {request.conversationId}",
                    )
            except Conversation.DoesNotExist:
                logger.error("Conversation not found", conversationId=request.conversationId)
                context.abort(
                    grpc.StatusCode.NOT_FOUND,
                    f"Conversation {request.conversationId} not found",
                )

            # Convert int enums to string enum names
            sender_type_name = communication_pb2.SenderType.Name(request.senderType)
            message_type_name = communication_pb2.MessageType.Name(request.type)
            message_status_name = None
            if request.HasField("status"):
                message_status_name = communication_pb2.MessageStatus.Name(request.status)

            # Convert protobuf Struct data to dictionary for storage
            message_data = None
            if request.HasField("data"):
                try:
                    # Use MessageToDict for proper conversion of all nested structures
                    from google.protobuf.json_format import MessageToDict

                    message_data = MessageToDict(request.data)
                except Exception as e:
                    logger.error("Failed to convert protobuf Struct to dictionary", error=str(e))
                    context.abort(
                        grpc.StatusCode.INVALID_ARGUMENT, f"Invalid data format: {str(e)}"
                    )

            # Populate the workflowResponse array field
            workflow_response_list = []
            if request.workflowResponse:
                from google.protobuf.json_format import MessageToDict
                for idx, struct_value in enumerate(request.workflowResponse):
                    try:
                        # Convert each Struct to dict
                        workflow_data = MessageToDict(struct_value)
                        workflow_response_list.append(workflow_data)
                    except Exception as e:
                        logger.error(
                            "Failed to process workflowResponse item",
                            index=idx,
                            error=str(e),
                        )

            # Create message in database
            message = Message(
                conversationId=request.conversationId,
                senderType=sender_type_name,
                data=message_data,
                workflowId=request.workflowId,
                workflowResponse=workflow_response_list,
                status=message_status_name,
                type=message_type_name,
            )
            message.save()

            # Update conversation title if not already set
            try:
                if not conversation.title and message_data:
                    # Extract title from message data
                    title = self._extract_title_from_message_data(message_data)
                    if title:
                        conversation.title = title
                        conversation.save()
                        logger.info(
                            "Updated conversation title",
                            conversationId=str(conversation.id),
                            title=title,
                        )
            except Exception as e:
                logger.warning("Failed to update conversation title", error=str(e))

            # Log success
            logger.info("Message created successfully", messageId=message.id)

            # Convert to protobuf message
            return message.to_proto()

        except Exception as e:
            # Log error
            logger.error("Failed to create message", error=str(e))

            # Raise gRPC exception
            context.abort(grpc.StatusCode.INTERNAL, f"Failed to create message: {str(e)}")

    # Delete message
    def deleteMessage(
        self,
        request: communication_pb2.DeleteMessageRequest,
        context: grpc.ServicerContext,
    ) -> empty_pb2.Empty:
        """
        Delete a message.

        Args:
            request: DeleteMessageRequest containing message ID and user ID
            context: gRPC servicer context

        Returns:
            Empty response if the user is authorized
        """
        try:
            # Log request details
            logger.info("Deleting message", messageId=request.messageId, userId=request.userId)

            # Get the message
            try:
                message = Message.objects.get(id=request.messageId)
            except Message.DoesNotExist:
                logger.error("Message not found", messageId=request.messageId)
                context.abort(grpc.StatusCode.NOT_FOUND, f"Message {request.messageId} not found")

            # Check if the user is authorized to delete this message
            try:
                conversation = Conversation.objects.get(id=message.conversationId)
                if conversation.userId != request.userId:
                    logger.warning(
                        "Unauthorized message deletion attempt",
                        messageId=request.messageId,
                        conversationId=str(message.conversationId),
                        requestUserId=request.userId,
                        conversationUserId=conversation.userId,
                    )
                    context.abort(
                        grpc.StatusCode.PERMISSION_DENIED,
                        f"User {request.userId} is not authorized to delete message {request.messageId}",
                    )
            except Conversation.DoesNotExist:
                logger.error("Conversation not found", conversationId=str(message.conversationId))
                context.abort(
                    grpc.StatusCode.NOT_FOUND,
                    f"Conversation {message.conversationId} not found",
                )

            # Delete message from database
            message.delete()

            # Log success
            logger.info("Message deleted successfully", messageId=request.messageId)

            # Return empty response
            return empty_pb2.Empty()

        except Exception as e:
            # Log error
            logger.error("Failed to delete message", error=str(e))

            # Raise gRPC exception
            context.abort(grpc.StatusCode.INTERNAL, f"Failed to delete message: {str(e)}")

    # List messages
    def listMessages(
        self,
        request: communication_pb2.ListMessagesRequest,
        context: grpc.ServicerContext,
    ) -> communication_pb2.ListMessagesResponse:
        """
        List messages for a conversation with JSON data support.

        Args:
            request: ListMessagesRequest containing conversation ID and user ID
            context: gRPC servicer context

        Returns:
            List of messages with JSON data if the user is authorized
        """
        try:
            # Log request details
            logger.info(
                "Listing messages",
                conversationId=request.conversationId,
                userId=request.userId,
            )

            # Check if the user is authorized to list messages in this conversation
            try:
                conversation = Conversation.objects.get(id=request.conversationId)
                if conversation.userId != request.userId:
                    logger.warning(
                        "Unauthorized message listing attempt",
                        conversationId=request.conversationId,
                        requestUserId=request.userId,
                        conversationUserId=conversation.userId,
                    )
                    context.abort(
                        grpc.StatusCode.PERMISSION_DENIED,
                        f"User {request.userId} is not authorized to list messages in conversation {request.conversationId}",
                    )
            except Conversation.DoesNotExist:
                logger.error("Conversation not found", conversationId=request.conversationId)
                context.abort(
                    grpc.StatusCode.NOT_FOUND,
                    f"Conversation {request.conversationId} not found",
                )

            # Pagination parameters with robust type checking
            try:
                page_size = int(request.limit)
                if page_size <= 0:
                    page_size = 10
            except (TypeError, ValueError):
                page_size = 10

            try:
                page = int(request.page)
                if page <= 0:
                    page = 1
            except (TypeError, ValueError):
                page = 1

            skip = (page - 1) * page_size

            # Build message filter query for the specified conversation
            message_filter = Message.objects.filter(conversationId=request.conversationId)

            # Get paginated results
            messages = message_filter[skip : skip + page_size]
            total_count = message_filter.count()
            total_pages = (total_count + page_size - 1) // page_size

            # Log success
            logger.info("Messages listed successfully", conversationId=request.conversationId)

            # Build pagination metadata
            metadata = communication_pb2.PaginationMetadata(
                total=total_count,
                totalPages=total_pages,
                currentPage=page,
                pageSize=page_size,
                hasNextPage=page < total_pages,
                hasPreviousPage=page > 1,
            )

            # Convert to protobuf messages with debug logging for JSON data
            proto_messages = []
            for idx, message in enumerate(messages):
                try:
                    proto = message.to_proto()
                    if not hasattr(proto, "DESCRIPTOR"):
                        logger.error(
                            "to_proto() did not return a protobuf message",
                            index=idx,
                            type=str(type(proto)),
                            value=str(proto),
                        )
                    proto_messages.append(proto)
                except Exception as e:
                    logger.error(
                        "Exception in message.to_proto()",
                        index=idx,
                        error=str(e),
                        message_repr=repr(message),
                    )
            logger.info(
                "Returning ListMessagesResponse with JSON data support",
                proto_types=[str(type(m)) for m in proto_messages],
                count=len(proto_messages),
            )
            return communication_pb2.ListMessagesResponse(
                data=proto_messages,
                metadata=metadata,
            )

        except Exception as e:
            # Log error
            logger.error("Failed to list messages", error=str(e))

            # Raise gRPC exception
            context.abort(grpc.StatusCode.INTERNAL, f"Failed to list messages: {str(e)}")

    def _extract_title_from_message_data(self, message_data):
        """
        Extract a title from message data.

        Args:
            message_data: Dictionary containing message data

        Returns:
            str: Extracted title or None
        """
        if not message_data:
            return None

        # Try to extract title from common fields
        if isinstance(message_data, dict):
            # Look for common title fields
            for field in ["title", "subject", "content", "text", "message"]:
                if field in message_data and message_data[field]:
                    title = str(message_data[field]).strip()
                    # Truncate if too long
                    if len(title) > 100:
                        title = title[:97] + "..."
                    return title

            # If no specific title field, try to extract from first available text field
            for key, value in message_data.items():
                if isinstance(value, str) and value.strip():
                    title = value.strip()
                    # Truncate if too long
                    if len(title) > 100:
                        title = title[:97] + "..."
                    return title

        return None

    # Update message workflow response
    def updateMessageWorkflowResponse(
        self,
        request: communication_pb2.UpdateMessageWorkflowResponseRequest,
        context: grpc.ServicerContext,
    ) -> communication_pb2.Message:
        """
        Update the workflowResponse array of a message by appending new data.

        Args:
            request: UpdateMessageWorkflowResponseRequest containing message ID, new workflow response data, and user ID
            context: gRPC servicer context

        Returns:
            Updated message if the user is authorized
        """
        try:
            # Log request details
            logger.info(
                "Updating message workflow response",
                messageId=request.messageId,
                userId=request.userId,
                hasNewResponse=bool(request.HasField("newWorkflowResponse")),
            )

            # Get the message
            try:
                message = Message.objects.get(id=request.messageId)
            except Message.DoesNotExist:
                logger.error("Message not found", messageId=request.messageId)
                context.abort(
                    grpc.StatusCode.NOT_FOUND, f"Message {request.messageId} not found"
                )

            # Check if the user is authorized to update this message
            try:
                conversation = Conversation.objects.get(id=message.conversationId)
                if conversation.userId != request.userId:
                    logger.warning(
                        "Unauthorized message workflow response update attempt",
                        messageId=request.messageId,
                        conversationId=str(message.conversationId),
                        requestUserId=request.userId,
                        conversationUserId=conversation.userId,
                    )
                    context.abort(
                        grpc.StatusCode.PERMISSION_DENIED,
                        f"User {request.userId} is not authorized to update message {request.messageId}",
                    )
            except Conversation.DoesNotExist:
                logger.error("Conversation not found", conversationId=str(message.conversationId))
                context.abort(
                    grpc.StatusCode.NOT_FOUND,
                    f"Conversation {message.conversationId} not found",
                )

            # Process the new workflow response data
            if request.HasField("newWorkflowResponse"):
                try:
                    # Initialize workflowResponse as empty list if it's None
                    if message.workflowResponse is None:
                        message.workflowResponse = []
                    
                    # Convert protobuf Struct (JSON) to Python dict
                    from google.protobuf.json_format import MessageToDict
                    workflow_data = MessageToDict(request.newWorkflowResponse)
                    
                    # Append the JSON data to the array
                    message.workflowResponse.append(workflow_data)
                    
                    # Save the updated message
                    message.save()
                    
                    logger.info(
                        "Message workflow response updated successfully",
                        messageId=request.messageId,
                        newArrayLength=len(message.workflowResponse),
                    )
                    
                except Exception as e:
                    logger.error(
                        "Failed to process new workflow response",
                        messageId=request.messageId,
                        error=str(e),
                    )
                    context.abort(
                        grpc.StatusCode.INVALID_ARGUMENT,
                        f"Invalid workflow response data: {str(e)}",
                    )
            else:
                logger.warning(
                    "No new workflow response data provided",
                    messageId=request.messageId,
                )
                context.abort(
                    grpc.StatusCode.INVALID_ARGUMENT,
                    "No new workflow response data provided",
                )

            # Convert to protobuf message and return
            return message.to_proto()

        except Exception as e:
            # Log error
            logger.error("Failed to update message workflow response", error=str(e))

            # Raise gRPC exception
            context.abort(
                grpc.StatusCode.INTERNAL,
                f"Failed to update message workflow response: {str(e)}",
            )

    # Update message status
    def updateMessageStatus(
        self,
        request: communication_pb2.UpdateMessageStatusRequest,
        context: grpc.ServicerContext,
    ) -> communication_pb2.Message:
        """
        Update the status of a message.

        Args:
            request: UpdateMessageStatusRequest containing message ID, new status, and user ID
            context: gRPC servicer context

        Returns:
            Updated message if the user is authorized
        """
        try:
            # Log request details
            logger.info(
                "Updating message status",
                messageId=request.messageId,
                userId=request.userId,
                newStatus=communication_pb2.MessageStatus.Name(request.status),
            )

            # Get the message
            try:
                message = Message.objects.get(id=request.messageId)
            except Message.DoesNotExist:
                logger.error("Message not found", messageId=request.messageId)
                context.abort(
                    grpc.StatusCode.NOT_FOUND, f"Message {request.messageId} not found"
                )

            # Check if the user is authorized to update this message
            try:
                conversation = Conversation.objects.get(id=message.conversationId)
                if conversation.userId != request.userId:
                    logger.warning(
                        "Unauthorized message status update attempt",
                        messageId=request.messageId,
                        conversationId=str(message.conversationId),
                        requestUserId=request.userId,
                        conversationUserId=conversation.userId,
                    )
                    context.abort(
                        grpc.StatusCode.PERMISSION_DENIED,
                        f"User {request.userId} is not authorized to update message {request.messageId}",
                    )
            except Conversation.DoesNotExist:
                logger.error("Conversation not found", conversationId=str(message.conversationId))
                context.abort(
                    grpc.StatusCode.NOT_FOUND,
                    f"Conversation {message.conversationId} not found",
                )

            # Convert int enum to string enum name for storage
            status_name = communication_pb2.MessageStatus.Name(request.status)
            
            # Validate that the status is not UNSPECIFIED
            if status_name == "MESSAGE_STATUS_UNSPECIFIED":
                logger.warning(
                    "Attempt to set message status to UNSPECIFIED",
                    messageId=request.messageId,
                    userId=request.userId,
                )
                context.abort(
                    grpc.StatusCode.INVALID_ARGUMENT,
                    "Cannot set message status to MESSAGE_STATUS_UNSPECIFIED",
                )

            # Update the message status
            old_status = message.status
            message.status = status_name
            
            # Save the updated message
            message.save()
            
            logger.info(
                "Message status updated successfully",
                messageId=request.messageId,
                oldStatus=old_status,
                newStatus=status_name,
            )

            # Convert to protobuf message and return
            return message.to_proto()

        except Exception as e:
            # Log error
            logger.error("Failed to update message status", error=str(e))

            # Raise gRPC exception
            context.abort(
                grpc.StatusCode.INTERNAL,
                f"Failed to update message status: {str(e)}",
            )
