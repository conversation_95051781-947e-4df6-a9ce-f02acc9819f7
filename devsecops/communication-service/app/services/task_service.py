"""
Service implementation for handling task-related operations.
"""

# Standard libraries
import grpc
from google.protobuf import empty_pb2

# Local imports
from app.grpc_ import communication_pb2, communication_pb2_grpc
from app.models.conversation_model import Conversation
from app.models.task_model import Task
from app.utils.logger import setup_logger

# Initialize logger
logger = setup_logger("communication-service/services/task_service.py")


# Task service class
class TaskService(communication_pb2_grpc.CommunicationServiceServicer):
    """
    Service class for handling task-related operations.
    Implements CRUD operations for tasks.
    """

    # Create task
    def createTask(
        self,
        request: communication_pb2.CreateTaskRequest,
        context: grpc.ServicerContext,
    ) -> communication_pb2.Task:
        """
        Create a new task.

        Args:
            request: CreateTaskRequest containing task details and user ID
            context: gRPC servicer context

        Returns:
            Created task if the user is authorized
        """
        try:
            # Log request details
            logger.info(
                "Creating new task",
                title=request.title,
                globalChatConversationId=request.globalChatConversationId,
                agentConversationId=request.agentConversationId,
                agentId=request.agentId,
                correlationId=request.correlationId,
                taskStatus=request.taskStatus,
                sessionId=request.sessionId,
            )

            # Check if the user is authorized to create a task in this conversation
            try:
                conversation = Conversation.objects.get(id=request.globalChatConversationId)
                if conversation.userId != request.userId:
                    logger.warning(
                        "Unauthorized task creation attempt",
                        globalChatConversationId=request.globalChatConversationId,
                        requestUserId=request.userId,
                        conversationUserId=conversation.userId,
                    )
                    context.abort(
                        grpc.StatusCode.PERMISSION_DENIED,
                        f"User {request.userId} is not authorized to create tasks in conversation {request.globalChatConversationId}",
                    )
            except Conversation.DoesNotExist:
                logger.error(
                    "Conversation not found", conversationId=request.globalChatConversationId
                )
                context.abort(
                    grpc.StatusCode.NOT_FOUND,
                    f"Conversation {request.globalChatConversationId} not found",
                )

            # Convert int enum to string enum name
            task_status_name = communication_pb2.TaskStatus.Name(request.taskStatus)

            # Create task in database
            task = Task(
                title=request.title,
                globalChatConversationId=request.globalChatConversationId,
                agentConversationId=request.agentConversationId,
                agentId=request.agentId,
                correlationId=request.correlationId if request.correlationId else None,
                taskStatus=task_status_name,
                sessionId=request.sessionId if request.sessionId else None,
            )
            task.save()

            # Log success
            logger.info("Task created successfully", taskId=task.id)

            # Convert to protobuf message
            return task.to_proto()

        except Exception as e:
            # Log error
            logger.error("Failed to create task", error=str(e))

            # Raise gRPC exception
            context.abort(
                grpc.StatusCode.INTERNAL, f"Failed to create task: {str(e)}"
            )

    # Delete task
    def deleteTask(
        self,
        request: communication_pb2.DeleteTaskRequest,
        context: grpc.ServicerContext,
    ) -> empty_pb2.Empty:
        """
        Delete a task.

        Args:
            request: DeleteTaskRequest containing task ID and user ID
            context: gRPC servicer context

        Returns:
            Empty response if the user is authorized
        """
        try:
            # Log request details
            logger.info(
                "Deleting task", taskId=request.taskId, userId=request.userId
            )

            # Get the task
            try:
                task = Task.objects.get(id=request.taskId)
            except Task.DoesNotExist:
                logger.error("Task not found", taskId=request.taskId)
                context.abort(
                    grpc.StatusCode.NOT_FOUND, f"Task {request.taskId} not found"
                )

            # Check if the user is authorized to delete this task
            try:
                conversation = Conversation.objects.get(id=task.globalChatConversationId)
                if conversation.userId != request.userId:
                    logger.warning(
                        "Unauthorized task deletion attempt",
                        taskId=request.taskId,
                        globalChatConversationId=str(task.globalChatConversationId),
                        requestUserId=request.userId,
                        conversationUserId=conversation.userId,
                    )
                    context.abort(
                        grpc.StatusCode.PERMISSION_DENIED,
                        f"User {request.userId} is not authorized to delete task {request.taskId}",
                    )
            except Conversation.DoesNotExist:
                logger.error(
                    "Conversation not found", conversationId=str(task.globalChatConversationId)
                )
                context.abort(
                    grpc.StatusCode.NOT_FOUND,
                    f"Conversation {task.globalChatConversationId} not found",
                )

            # Delete task from database
            task.delete()

            # Log success
            logger.info("Task deleted successfully", taskId=request.taskId)

            # Return empty response
            return empty_pb2.Empty()

        except Exception as e:
            # Log error
            logger.error("Failed to delete task", error=str(e))

            # Raise gRPC exception
            context.abort(
                grpc.StatusCode.INTERNAL, f"Failed to delete task: {str(e)}"
            )

    # Update task status
    def updateTaskStatus(
        self,
        request: communication_pb2.UpdateTaskStatusRequest,
        context: grpc.ServicerContext,
    ) -> empty_pb2.Empty:
        """
        Update the status of a task.

        Args:
            request: UpdateTaskStatusRequest containing task ID, new status, and user ID
            context: gRPC servicer context

        Returns:
            Empty response if the user is authorized
        """
        try:
            # Log request details
            logger.info(
                "Updating task status",
                taskId=request.taskId,
                taskStatus=request.taskStatus,
                userId=request.userId,
            )

            # Get the task
            try:
                task = Task.objects.get(id=request.taskId)
            except Task.DoesNotExist:
                logger.error("Task not found", taskId=request.taskId)
                context.abort(
                    grpc.StatusCode.NOT_FOUND, f"Task {request.taskId} not found"
                )

            # Check if the user is authorized to update this task
            try:
                conversation = Conversation.objects.get(id=task.globalChatConversationId)
                if conversation.userId != request.userId:
                    logger.warning(
                        "Unauthorized task status update attempt",
                        taskId=request.taskId,
                        globalChatConversationId=str(task.globalChatConversationId),
                        requestUserId=request.userId,
                        conversationUserId=conversation.userId,
                    )
                    context.abort(
                        grpc.StatusCode.PERMISSION_DENIED,
                        f"User {request.userId} is not authorized to update task {request.taskId}",
                    )
            except Conversation.DoesNotExist:
                logger.error(
                    "Conversation not found", conversationId=str(task.globalChatConversationId)
                )
                context.abort(
                    grpc.StatusCode.NOT_FOUND,
                    f"Conversation {task.globalChatConversationId} not found",
                )

            # Convert int enum to string enum name
            task_status_name = communication_pb2.TaskStatus.Name(request.taskStatus)

            # Update task status
            task.taskStatus = task_status_name
            task.save()

            # Log success
            logger.info("Task status updated successfully", taskId=request.taskId, newStatus=task_status_name)

            # Return empty response
            return empty_pb2.Empty()

        except Exception as e:
            # Log error
            logger.error("Failed to update task status", error=str(e))

            # Raise gRPC exception
            context.abort(
                grpc.StatusCode.INTERNAL, f"Failed to update task status: {str(e)}"
            )

    # List tasks
    def listTasks(
        self,
        request: communication_pb2.ListTasksRequest,
        context: grpc.ServicerContext,
    ) -> communication_pb2.ListTasksResponse:
        """
        List tasks with enhanced filtering options.
        
        Supports 5 filtering modes:
        1. All tasks (if no globalChatConversationId) - returns all user's tasks ordered by latest first
        2. By globalChatConversationId - filters tasks for specific conversation
        3. By agentId - filters tasks by specific agent
        4. By search query - searches in task titles using regex
        5. By taskStatus - filters tasks by specific status

        Args:
            request: ListTasksRequest containing query parameters
            context: gRPC servicer context

        Returns:
            List of tasks matching the filters if user is authorized
        """
        try:
            # Log request details
            logger.info(
                "Listing tasks with filters",
                globalChatConversationId=request.globalChatConversationId if request.globalChatConversationId else "ALL",
                userId=request.userId,
                agentId=getattr(request, 'agentId', None) if hasattr(request, 'agentId') else None,
                search=getattr(request, 'search', None) if hasattr(request, 'search') else None,
                taskStatus=getattr(request, 'taskStatus', None) if hasattr(request, 'taskStatus') else None,
            )

            # Pagination parameters with robust type checking
            try:
                page_size = int(request.limit)
                if page_size <= 0:
                    page_size = 10
            except (TypeError, ValueError):
                page_size = 10

            try:
                page = int(request.page)
                if page <= 0:
                    page = 1
            except (TypeError, ValueError):
                page = 1

            skip = (page - 1) * page_size

            # Initialize MongoDB query for advanced filtering
            from mongoengine import Q
            
            # Start with a base query - no restrictions initially
            query = Q()
            user_authorized_conversation_ids = []

            # If globalChatConversationId is provided, check authorization and filter by it
            if request.globalChatConversationId:
                # Check if the user is authorized to access this specific conversation
                try:
                    conversation = Conversation.objects.get(id=request.globalChatConversationId)
                    if conversation.userId != request.userId:
                        logger.warning(
                            "Unauthorized task listing attempt for specific conversation",
                            globalChatConversationId=request.globalChatConversationId,
                            requestUserId=request.userId,
                            conversationUserId=conversation.userId,
                        )
                        context.abort(
                            grpc.StatusCode.PERMISSION_DENIED,
                            f"User {request.userId} is not authorized to list tasks in conversation {request.globalChatConversationId}",
                        )
                    user_authorized_conversation_ids = [request.globalChatConversationId]
                    query = query & Q(globalChatConversationId=request.globalChatConversationId)
                    logger.info("Applying globalChatConversationId filter", conversationId=request.globalChatConversationId)
                except Conversation.DoesNotExist:
                    logger.error(
                        "Conversation not found", conversationId=request.globalChatConversationId
                    )
                    context.abort(
                        grpc.StatusCode.NOT_FOUND,
                        f"Conversation {request.globalChatConversationId} not found",
                    )
            else:
                # No specific conversation provided - get all conversations user has access to
                user_conversations = Conversation.objects.filter(userId=request.userId)
                user_authorized_conversation_ids = [str(conv.id) for conv in user_conversations]
                
                if user_authorized_conversation_ids:
                    query = query & Q(globalChatConversationId__in=user_authorized_conversation_ids)
                    logger.info("Applying user authorization filter", conversationCount=len(user_authorized_conversation_ids))
                else:
                    # User has no conversations, return empty result
                    logger.info("User has no conversations, returning empty result", userId=request.userId)
                    metadata = communication_pb2.PaginationMetadata(
                        total=0, totalPages=0, currentPage=page, pageSize=page_size,
                        hasNextPage=False, hasPreviousPage=False,
                    )
                    return communication_pb2.ListTasksResponse(data=[], metadata=metadata)

            # Filter 3: By agentId - add agentId filter if provided
            agent_id = getattr(request, 'agentId', None) if hasattr(request, 'agentId') else None
            if agent_id:
                query = query & Q(agentId=agent_id)
                logger.info("Applying agentId filter", agentId=agent_id)

            # Filter 4: By search query - search in task titles using regex
            search_query = getattr(request, 'search', None) if hasattr(request, 'search') else None
            if search_query and search_query.strip():
                # Use MongoDB regex for case-insensitive search in title field
                # Escape special regex characters to prevent injection
                import re
                escaped_search = re.escape(search_query.strip())
                # Create case-insensitive regex pattern
                title_regex = f".*{escaped_search}.*"
                query = query & Q(title__iregex=title_regex)
                logger.info("Applying search filter", searchQuery=search_query)

            # Filter 5: By taskStatus - add taskStatus filter if provided
            task_status = getattr(request, 'taskStatus', None) if hasattr(request, 'taskStatus') else None
            if task_status and task_status != 0:  # 0 is TASK_STATUS_UNSPECIFIED
                task_status_name = communication_pb2.TaskStatus.Name(task_status)
                query = query & Q(taskStatus=task_status_name)
                logger.info("Applying taskStatus filter", taskStatus=task_status_name)

            # Apply the query and order by creation date (latest first)
            task_filter = Task.objects(query).order_by("-updatedAt")

            # Get paginated results
            tasks = task_filter[skip : skip + page_size]
            total_count = task_filter.count()
            total_pages = (total_count + page_size - 1) // page_size

            # Log success with filter summary
            logger.info(
                "Tasks listed successfully with filters applied",
                userId=request.userId,
                globalChatConversationId=request.globalChatConversationId if request.globalChatConversationId else "ALL",
                agentId=agent_id if agent_id else "ALL",
                search=search_query if search_query else "NONE",
                taskStatus=task_status_name if task_status and task_status != 0 else "ALL",
                totalFound=total_count,
                pageSize=page_size,
                currentPage=page,
            )

            # Build pagination metadata
            metadata = communication_pb2.PaginationMetadata(
                total=total_count,
                totalPages=total_pages,
                currentPage=page,
                pageSize=page_size,
                hasNextPage=page < total_pages,
                hasPreviousPage=page > 1,
            )

            # Convert to protobuf messages with debug logging
            proto_tasks = []
            for idx, task in enumerate(tasks):
                try:
                    proto = task.to_proto()
                    if not hasattr(proto, "DESCRIPTOR"):
                        logger.error(
                            "to_proto() did not return a protobuf message",
                            index=idx,
                            type=str(type(proto)),
                            value=str(proto),
                        )
                    proto_tasks.append(proto)
                except Exception as e:
                    logger.error(
                        "Exception in task.to_proto()",
                        index=idx,
                        error=str(e),
                        task_repr=repr(task),
                    )

            logger.info(
                "Returning ListTasksResponse",
                proto_types=[str(type(t)) for t in proto_tasks],
                count=len(proto_tasks),
            )
            
            return communication_pb2.ListTasksResponse(
                data=proto_tasks,
                metadata=metadata,
            )

        except Exception as e:
            # Log error with filter context
            logger.error(
                "Failed to list tasks",
                error=str(e),
                userId=request.userId,
                globalChatConversationId=request.globalChatConversationId if request.globalChatConversationId else "ALL",
                agentId=getattr(request, 'agentId', None) if hasattr(request, 'agentId') else None,
                search=getattr(request, 'search', None) if hasattr(request, 'search') else None,
                taskStatus=getattr(request, 'taskStatus', None) if hasattr(request, 'taskStatus') else None,
            )

            # Raise gRPC exception
            context.abort(
                grpc.StatusCode.INTERNAL, f"Failed to list tasks: {str(e)}"
            )

    # Get task counts by status
    def getTaskCountsByStatus(
        self,
        request: communication_pb2.GetTaskCountsByStatusRequest,
        context: grpc.ServicerContext,
    ) -> communication_pb2.GetTaskCountsByStatusResponse:
        """
        Get task counts by status for multiple agents.
        
        Returns task statistics including:
        - total: Total count of all tasks for each agent
        - completed: Count of tasks with TASK_STATUS_COMPLETED
        - inProgress: Count of tasks with TASK_STATUS_RUNNING

        Args:
            request: GetTaskCountsByStatusRequest containing agent IDs
            context: gRPC servicer context

        Returns:
            GetTaskCountsByStatusResponse with task counts for each agent
        """
        try:
            # Log request details
            logger.info(
                "Getting task counts by status",
                agentIds=request.agentIds,
                agentCount=len(request.agentIds)
            )

            # Validate input
            if not request.agentIds:
                logger.warning("No agent IDs provided in request")
                context.abort(
                    grpc.StatusCode.INVALID_ARGUMENT,
                    "At least one agent ID must be provided"
                )

            # Initialize response dictionary
            task_counts_map = {}

            # Get task counts for each agent
            for agent_id in request.agentIds:
                try:
                    # Query all tasks for this agent
                    all_tasks = Task.objects.filter(agentId=agent_id)
                    total_count = all_tasks.count()

                    # Count completed tasks (TASK_STATUS_COMPLETED)
                    completed_tasks = all_tasks.filter(taskStatus="TASK_STATUS_COMPLETED")
                    completed_count = completed_tasks.count()

                    # Count in-progress tasks (TASK_STATUS_RUNNING)
                    in_progress_tasks = all_tasks.filter(taskStatus="TASK_STATUS_RUNNING")
                    in_progress_count = in_progress_tasks.count()

                    # Create TaskCounts message
                    task_counts = communication_pb2.TaskCounts(
                        total=total_count,
                        completed=completed_count,
                        inProgress=in_progress_count
                    )

                    # Add to response map
                    task_counts_map[agent_id] = task_counts

                    # Log individual agent results
                    logger.info(
                        "Task counts calculated for agent",
                        agentId=agent_id,
                        total=total_count,
                        completed=completed_count,
                        inProgress=in_progress_count
                    )

                except Exception as e:
                    # Log error for this specific agent but continue with others
                    logger.error(
                        "Failed to get task counts for agent",
                        agentId=agent_id,
                        error=str(e)
                    )
                    
                    # Return zero counts for this agent
                    task_counts = communication_pb2.TaskCounts(
                        total=0,
                        completed=0,
                        inProgress=0
                    )
                    task_counts_map[agent_id] = task_counts

            # Log overall success
            logger.info(
                "Task counts retrieved successfully",
                totalAgents=len(request.agentIds),
                processedAgents=len(task_counts_map)
            )

            # Create and return response
            return communication_pb2.GetTaskCountsByStatusResponse(
                taskCounts=task_counts_map
            )

        except Exception as e:
            # Log error
            logger.error(
                "Failed to get task counts by status",
                error=str(e),
                agentIds=request.agentIds
            )

            # Raise gRPC exception
            context.abort(
                grpc.StatusCode.INTERNAL,
                f"Failed to get task counts by status: {str(e)}"
            )
