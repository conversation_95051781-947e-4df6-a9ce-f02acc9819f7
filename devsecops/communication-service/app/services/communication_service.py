"""
Main communication service that combines conversation and message services.
"""

# Standard libraries
import grpc
from google.protobuf import empty_pb2

# Local imports
from app.grpc_ import communication_pb2, communication_pb2_grpc
from app.services.conversation_service import ConversationService
from app.services.message_service import MessageService
from app.services.task_service import TaskService
from app.utils.logger import setup_logger

# Initialize logger
logger = setup_logger("communication-service/services/communication_service.py")


# Communication service class
class CommunicationService(communication_pb2_grpc.CommunicationServiceServicer):
    """
    Main service class that combines conversation and message services.
    Delegates operations to appropriate service based on the request type.
    """

    # Initialize the communication service with conversation and message services
    def __init__(self):
        """
        Initialize the communication service with conversation, message, and task services.
        """

        try:
            # Initialize services
            self.conversation_service = ConversationService()
            self.message_service = MessageService()
            self.task_service = TaskService()

            # Log initialization
            logger.info("Communication service initialized successfully")

        except Exception as e:
            # Log error
            logger.error("Failed to initialize communication service", error=str(e))

            # Raise error
            raise Exception(f"Failed to initialize communication service: {str(e)}")

    # Create conversation
    def createConversation(
        self,
        request: communication_pb2.CreateConversationRequest,
        context: grpc.ServicerContext,
    ) -> communication_pb2.Conversation:
        """
        Create a new conversation.

        Args:
            request: CreateConversationRequest containing conversation details
            context: gRPC servicer context

        Returns:
            Created conversation
        """
        try:
            # Create conversation
            conversation = self.conversation_service.createConversation(
                request, context
            )

            # Return conversation
            return conversation

        except Exception as e:
            # Log error
            logger.error("Failed to create conversation", error=str(e))

            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create conversation: {str(e)}"
            )

    # Get conversation
    def getConversation(
        self,
        request: communication_pb2.GetConversationRequest,
        context: grpc.ServicerContext,
    ) -> communication_pb2.Conversation:
        """
        Get conversation by ID.

        Args:
            request: GetConversationRequest containing conversation ID
            context: gRPC servicer context

        Returns:
            Conversation with the specified ID
        """
        try:
            # Get conversation
            conversation = self.conversation_service.getConversation(request, context)

            # Return conversation
            return conversation

        except Exception as e:
            # Log error
            logger.error("Failed to get conversation", error=str(e))

            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to get conversation: {str(e)}"
            )

    # Delete conversation
    def deleteConversation(
        self,
        request: communication_pb2.DeleteConversationRequest,
        context: grpc.ServicerContext,
    ) -> empty_pb2.Empty:
        """
        Delete conversation by ID.

        Args:
            request: DeleteConversationRequest containing conversation ID
            context: gRPC servicer context

        Returns:
            Empty response
        """
        try:
            # Delete conversation
            self.conversation_service.deleteConversation(request, context)

            # Return empty response
            return empty_pb2.Empty()

        except Exception as e:
            # Log error
            logger.error("Failed to delete conversation", error=str(e))

            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to delete conversation: {str(e)}"
            )

    # List conversations
    def listConversations(
        self,
        request: communication_pb2.ListConversationsRequest,
        context: grpc.ServicerContext,
    ) -> communication_pb2.ListConversationsResponse:
        """
        List conversations.

        Args:
            request: ListConversationsRequest containing query parameters
            context: gRPC servicer context

        Returns:
            List of conversations
        """
        try:
            # List conversations
            conversations = self.conversation_service.listConversations(
                request, context
            )

            # Return conversations
            return conversations

        except Exception as e:
            # Log error
            logger.error("Failed to list conversations", error=str(e))

            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to list conversations: {str(e)}"
            )

    # Update conversation tokens
    def updateConversationTokens(
        self,
        request: communication_pb2.UpdateConversationTokensRequest,
        context: grpc.ServicerContext,
    ) -> empty_pb2.Empty:
        """
        Update conversation tokens.

        Args:
            request: UpdateConversationTokensRequest containing conversation ID, tokens, and user ID
            context: gRPC servicer context

        Returns:
            Empty response
        """
        try:
            # Update conversation tokens
            self.conversation_service.updateConversationTokens(request, context)

            # Return empty response
            return empty_pb2.Empty()

        except Exception as e:
            # Log error
            logger.error("Failed to update conversation tokens", error=str(e))

            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to update conversation tokens: {str(e)}"
            )

    # Create message
    def createMessage(
        self,
        request: communication_pb2.CreateMessageRequest,
        context: grpc.ServicerContext,
    ) -> communication_pb2.Message:
        """
        Create a new message.

        Args:
            request: CreateMessageRequest containing message details
            context: gRPC servicer context

        Returns:
            Created message
        """
        try:
            # Create message
            message = self.message_service.createMessage(request, context)

            # Return message
            return message

        except Exception as e:
            # Log error
            logger.error("Failed to create message", error=str(e))

            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create message: {str(e)}"
            )

    # Delete message
    def deleteMessage(
        self,
        request: communication_pb2.DeleteMessageRequest,
        context: grpc.ServicerContext,
    ) -> empty_pb2.Empty:
        """
        Delete a message.

        Args:
            request: DeleteMessageRequest containing message ID
            context: gRPC servicer context

        Returns:
            Empty response
        """
        try:
            # Delete message
            self.message_service.deleteMessage(request, context)

            # Return empty response
            return empty_pb2.Empty()

        except Exception as e:
            # Log error
            logger.error("Failed to delete message", error=str(e))

            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to delete message: {str(e)}"
            )

    # List messages
    def listMessages(
        self,
        request: communication_pb2.ListMessagesRequest,
        context: grpc.ServicerContext,
    ) -> communication_pb2.ListMessagesResponse:
        """
        List messages.

        Args:
            request: ListMessagesRequest containing query parameters
            context: gRPC servicer context

        Returns:
            List of messages
        """
        try:
            # List messages
            messages = self.message_service.listMessages(request, context)

            # Return messages
            return messages

        except Exception as e:
            # Log error
            logger.error("Failed to list messages", error=str(e))

            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to list messages: {str(e)}"
            )

    # Update message workflow response
    def updateMessageWorkflowResponse(
        self,
        request: communication_pb2.UpdateMessageWorkflowResponseRequest,
        context: grpc.ServicerContext,
    ) -> communication_pb2.Message:
        """
        Update the workflowResponse array of a message.

        Args:
            request: UpdateMessageWorkflowResponseRequest containing message ID, new workflow response data, and user ID
            context: gRPC servicer context

        Returns:
            Updated message
        """
        try:
            # Update message workflow response
            message = self.message_service.updateMessageWorkflowResponse(request, context)

            # Return updated message
            return message

        except Exception as e:
            # Log error
            logger.error("Failed to update message workflow response", error=str(e))

            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL,
                f"Failed to update message workflow response: {str(e)}",
            )

    # Update message status
    def updateMessageStatus(
        self,
        request: communication_pb2.UpdateMessageStatusRequest,
        context: grpc.ServicerContext,
    ) -> communication_pb2.Message:
        """
        Update the status of a message.

        Args:
            request: UpdateMessageStatusRequest containing message ID, new status, and user ID
            context: gRPC servicer context

        Returns:
            Updated message
        """
        try:
            # Update message status
            message = self.message_service.updateMessageStatus(request, context)

            # Return updated message
            return message

        except Exception as e:
            # Log error
            logger.error("Failed to update message status", error=str(e))

            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to update message status: {str(e)}"
            )

    # Create task
    def createTask(
        self,
        request: communication_pb2.CreateTaskRequest,
        context: grpc.ServicerContext,
    ) -> communication_pb2.Task:
        """
        Create a new task.

        Args:
            request: CreateTaskRequest containing task details
            context: gRPC servicer context

        Returns:
            Created task
        """
        try:
            # Create task
            task = self.task_service.createTask(request, context)

            # Return task
            return task

        except Exception as e:
            # Log error
            logger.error("Failed to create task", error=str(e))

            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to create task: {str(e)}"
            )

    # Delete task
    def deleteTask(
        self,
        request: communication_pb2.DeleteTaskRequest,
        context: grpc.ServicerContext,
    ) -> empty_pb2.Empty:
        """
        Delete a task.

        Args:
            request: DeleteTaskRequest containing task ID
            context: gRPC servicer context

        Returns:
            Empty response
        """
        try:
            # Delete task
            self.task_service.deleteTask(request, context)

            # Return empty response
            return empty_pb2.Empty()

        except Exception as e:
            # Log error
            logger.error("Failed to delete task", error=str(e))

            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to delete task: {str(e)}"
            )

    # List tasks
    def listTasks(
        self,
        request: communication_pb2.ListTasksRequest,
        context: grpc.ServicerContext,
    ) -> communication_pb2.ListTasksResponse:
        """
        List tasks.

        Args:
            request: ListTasksRequest containing query parameters
            context: gRPC servicer context

        Returns:
            List of tasks
        """
        try:
            # List tasks
            tasks = self.task_service.listTasks(request, context)

            # Return tasks
            return tasks

        except Exception as e:
            # Log error
            logger.error("Failed to list tasks", error=str(e))

            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to list tasks: {str(e)}"
            )

    # Update task status
    def updateTaskStatus(
        self,
        request: communication_pb2.UpdateTaskStatusRequest,
        context: grpc.ServicerContext,
    ) -> empty_pb2.Empty:
        """
        Update task status.

        Args:
            request: UpdateTaskStatusRequest containing task ID, new status, and user ID
            context: gRPC servicer context

        Returns:
            Empty response
        """
        try:
            # Update task status
            self.task_service.updateTaskStatus(request, context)

            # Return empty response
            return empty_pb2.Empty()

        except Exception as e:
            # Log error
            logger.error("Failed to update task status", error=str(e))

            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to update task status: {str(e)}"
            )

    # Get task counts by status
    def getTaskCountsByStatus(
        self,
        request: communication_pb2.GetTaskCountsByStatusRequest,
        context: grpc.ServicerContext,
    ) -> communication_pb2.GetTaskCountsByStatusResponse:
        """
        Get task counts by status for multiple agents.

        Args:
            request: GetTaskCountsByStatusRequest containing agent IDs
            context: gRPC servicer context

        Returns:
            GetTaskCountsByStatusResponse with task counts for each agent
        """
        try:
            # Get task counts by status
            task_counts = self.task_service.getTaskCountsByStatus(request, context)

            # Return task counts
            return task_counts

        except Exception as e:
            # Log error
            logger.error("Failed to get task counts by status", error=str(e))

            # Raise error
            raise grpc.RpcError(
                grpc.StatusCode.INTERNAL, f"Failed to get task counts by status: {str(e)}"
            )
