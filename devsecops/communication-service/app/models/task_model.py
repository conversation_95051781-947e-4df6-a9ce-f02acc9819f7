"""
MongoDB model definition for tasks in the communication service.

This module defines the Task document model and related enums using mongoengine ODM.
It includes comprehensive validation for different task types and their associated
statuses.
"""

# Standard library imports
from datetime import datetime

# Third-party imports

from mongoengine import DateTimeField, Document, StringField, ObjectIdField
from mongoengine.errors import ValidationError

# Local imports
from app.grpc_ import communication_pb2


# Task model
class Task(Document):
    """
    MongoDB document model for tasks in conversations.

    This model represents a task sent by a user or agent in a conversation,
    storing metadata such as task status, correlation ID, session ID, and timestamps.

    Attributes:
        globalChatConversationId (ObjectIdField): The ID of the global chat conversation to which the task belongs.
        agentConversationId (ObjectIdField): The ID of the agent conversation to which the task belongs.
        agentId (StringField): The ID of the agent associated with the task.
        correlationId (StringField): Optional correlation ID for the task.
        taskStatus (StringField): The status of the task.
        sessionId (StringField): Optional session ID for the task.
        createdAt (DateTimeField): Timestamp for when the task was created.
        updatedAt (DateTimeField): Timestamp for when the task was last updated.
    """

    # MongoDB collection & index configuration
    meta = {
        "collection": "tasks",
        "indexes": [
            # Single field indexes for the frequent queries
            "globalChatConversationId",
            "agentConversationId",
            "agentId",
            "correlationId",
            "taskStatus",
            "sessionId",
            # Text index for title search
            {"fields": ["title"], "cls": False},
            # Compound indexes for time-based queries
            ("globalChatConversationId", "createdAt"),
            # Compound indexes for filtered queries
            ("globalChatConversationId", "agentId"),
            ("globalChatConversationId", "taskStatus"),
            ("globalChatConversationId", "agentId", "taskStatus"),
            # Global indexes for user-wide filtering
            ("agentId", "createdAt"),
            ("taskStatus", "createdAt"),
        ],
        "ordering": ["-createdAt"],
    }

    # Title field for the task
    title = StringField(required=True)

    # Conversation ID field
    globalChatConversationId = ObjectIdField(required=True)

    # Agent conversation ID field
    agentConversationId = ObjectIdField(required=True)

    # Agent ID field
    agentId = StringField(required=True)

    # Correlation ID field (optional)
    correlationId = StringField(required=False)

    # Task status field
    taskStatus = StringField(required=True)

    # Session ID field (optional)
    sessionId = StringField(required=False)

    # Timestamp field for the creation of the task
    createdAt = DateTimeField(default=datetime.utcnow)

    # Timestamp field for the last update to the task
    updatedAt = DateTimeField(default=datetime.utcnow)

    # Clean the task model
    def clean(self):
        """Update the updated timestamp and validate the task status before saving."""

        # If the creation timestamp is not set
        if not self.createdAt:
            # Set the creation timestamp to the current time
            self.createdAt = datetime.utcnow()

        # Update the updated timestamp
        self.updatedAt = datetime.utcnow()

        # Get task status enum names
        task_status_names = [name for name in communication_pb2.TaskStatus.keys()]

        # If the task status value is not valid
        if self.taskStatus not in task_status_names:
            # Raise a validation error
            raise ValidationError(
                f"Invalid task status: {self.taskStatus}. Must be one of: {task_status_names}"
            )

        # Call the parent clean method
        super().clean()

    # String representation of the task document
    def __str__(self):
        """Return the string representation of the task document."""

        # Return the string representation of the task document
        return f"Task({self.id}, {self.title}, {self.globalChatConversationId}, {self.agentConversationId}, {self.agentId}, {self.taskStatus}, {self.sessionId})"

    # Convert the task document to a dictionary
    def to_dict(self):
        """Convert the task document to a dictionary."""

        # Return the task document as a dictionary
        return {
            "id": str(self.id),
            "title": self.title,
            "globalChatConversationId": str(self.globalChatConversationId),
            "agentConversationId": str(self.agentConversationId),
            "agentId": str(self.agentId),
            "correlationId": self.correlationId,
            "taskStatus": self.taskStatus,
            "sessionId": self.sessionId,
            "createdAt": self.createdAt,
            "updatedAt": self.updatedAt,
        }

    # Convert the task document to a gRPC message
    def to_proto(self):
        """Convert the task document to a gRPC message."""

        # Create a new task
        task = communication_pb2.Task()

        # Set the message ID
        from app.utils.logger import setup_logger

        logger = setup_logger("communication-service/models/task_model.py")

        # Set the task ID
        try:
            task.id = str(self.id)
        except Exception as e:
            logger.error("Error setting id", value=repr(self.id), error=str(e))

        # Set the title
        try:
            task.title = str(self.title)
        except Exception as e:
            logger.error("Error setting title", value=repr(self.title), error=str(e))

        # Set the global chat conversation ID
        try:
            task.globalChatConversationId = str(self.globalChatConversationId)
        except Exception as e:
            logger.error(
                "Error setting globalChatConversationId",
                value=repr(self.globalChatConversationId),
                error=str(e),
            )

        # Set the agent conversation ID
        try:
            task.agentConversationId = str(self.agentConversationId)
        except Exception as e:
            logger.error(
                "Error setting agentConversationId",
                value=repr(self.agentConversationId),
                error=str(e),
            )

        # Set the agent ID
        try:
            task.agentId = str(self.agentId)
        except Exception as e:
            logger.error("Error setting agentId", value=repr(self.agentId), error=str(e))

        # Set the correlation ID if it exists
        try:
            if self.correlationId:
                task.correlationId = str(self.correlationId)
        except Exception as e:
            logger.error(
                "Error setting correlationId", value=repr(self.correlationId), error=str(e)
            )

        # Set the session ID if it exists
        try:
            if self.sessionId:
                task.sessionId = str(self.sessionId)
        except Exception as e:
            logger.error("Error setting sessionId", value=repr(self.sessionId), error=str(e))

        # Convert string task status to enum for protobuf
        try:
            if self.taskStatus:
                task.taskStatus = communication_pb2.TaskStatus.Value(self.taskStatus)
        except Exception as e:
            logger.error("Error setting taskStatus", value=repr(self.taskStatus), error=str(e))

        from datetime import datetime as dt

        # Set the createdAt
        try:
            if isinstance(self.createdAt, dt):
                task.createdAt.FromDatetime(self.createdAt)
            elif self.createdAt is not None:
                try:
                    task.createdAt.FromDatetime(dt.fromisoformat(str(self.createdAt)))
                except Exception as e2:
                    logger.warning(
                        "Invalid createdAt, using current time",
                        createdAt=str(self.createdAt),
                        error=str(e2),
                    )
                    task.createdAt.FromDatetime(dt.utcnow())
            else:
                task.createdAt.FromDatetime(dt.utcnow())
        except Exception as e:
            logger.error("Error setting createdAt", value=repr(self.createdAt), error=str(e))

        # updatedAt
        try:
            if isinstance(self.updatedAt, dt):
                task.updatedAt.FromDatetime(self.updatedAt)
            elif self.updatedAt is not None:
                try:
                    task.updatedAt.FromDatetime(dt.fromisoformat(str(self.updatedAt)))
                except Exception as e2:
                    logger.warning(
                        "Invalid updatedAt, using current time",
                        updatedAt=str(self.updatedAt),
                        error=str(e2),
                    )
                    task.updatedAt.FromDatetime(dt.utcnow())
            else:
                task.updatedAt.FromDatetime(dt.utcnow())
        except Exception as e:
            logger.error("Error setting updatedAt", value=repr(self.updatedAt), error=str(e))

        # Return the task
        return task
