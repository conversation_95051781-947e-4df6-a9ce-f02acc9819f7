"""Communication Service gRPC Server

This module implements a gRPC server for the communication service with health checking capabilities.
It provides both the main service functionality and health status reporting.
"""

# Standard library imports
import signal
import sys
from concurrent import futures

# Third-party imports
import grpc
from grpc_health.v1 import health, health_pb2, health_pb2_grpc

# Local imports
from app.core.config import settings
from app.db import close_db
from app.db.mongo import init_mongo_from_env, create_indexes
from app.grpc_ import communication_pb2_grpc
from app.services.communication_service import CommunicationService
from app.utils.logger import setup_logger

# Initialize logger
logger = setup_logger("communication-service/main.py")

# Global server instance for graceful shutdown
server = None


# Cleanup function
def cleanup() -> None:
    """
    Cleanup resources before server shutdown.

    This function performs cleanup operations such as closing database connections
    when the server is shutting down.
    """
    try:
        # Close MongoDB connections
        close_db()
        logger.info("Closed MongoDB connections")

    except Exception as e:
        # Log error
        logger.error("Error during cleanup", error=str(e))

        # Raise error
        raise RuntimeError("Error during cleanup") from e


def signal_handler(signum, frame):
    """
    Handle shutdown signals gracefully.

    Args:
        signum: Signal number
        frame: Current stack frame
    """
    global server
    logger.info(f"Received signal {signum}, initiating graceful shutdown...")

    if server:
        # Graceful shutdown with timeout
        server.stop(grace=30)
        logger.info("Server stopped gracefully")

    # Cleanup resources
    cleanup()
    sys.exit(0)


# Serve function
def serve() -> None:
    """
    Initialize and run the gRPC server for the communication service.

    This function sets up a gRPC server with optimized configuration for handling
    concurrent requests. The server includes health checking service and the main
    communication service functionality.

    Environment Variables:
        GRPC_PORT (str): The port number for the gRPC server (default: "50055")

    Raises:
        Exception: If server initialization or startup fails
    """
    global server

    try:
        # Initialize MongoDB connection from environment variables
        init_mongo_from_env()

        # Create database indexes for better performance
        logger.info("Creating database indexes...")
        create_indexes()
        logger.info("Database indexes created successfully")

        # Create gRPC server with optimized configuration
        server = grpc.server(
            futures.ThreadPoolExecutor(max_workers=50),
            options=[
                ("grpc.keepalive_time_ms", 30000),
                ("grpc.keepalive_timeout_ms", 5000),
                ("grpc.keepalive_permit_without_calls", True),
                ("grpc.http2.max_pings_without_data", 0),
                ("grpc.http2.min_time_between_pings_ms", 10000),
                ("grpc.http2.min_ping_interval_without_data_ms", 300000),
                ("grpc.max_receive_message_length", 4 * 1024 * 1024),
                ("grpc.max_send_message_length", 4 * 1024 * 1024),
            ],
        )

        # Add health checking service
        health_servicer = health.HealthServicer(
            experimental_non_blocking=True,
            experimental_thread_pool=futures.ThreadPoolExecutor(max_workers=5),
        )
        health_pb2_grpc.add_HealthServicer_to_server(health_servicer, server)

        # Set initial health status
        health_servicer.set("", health_pb2.HealthCheckResponse.SERVING)
        health_servicer.set("grpc.health.v1.Health", health_pb2.HealthCheckResponse.SERVING)

        # Add the communication service & set health status
        communication_pb2_grpc.add_CommunicationServiceServicer_to_server(
            CommunicationService(), server
        )
        health_servicer.set(
            "communication.communicationService", health_pb2.HealthCheckResponse.SERVING
        )

        # Configure server port
        port = settings.GRPC_PORT
        server.add_insecure_port(f"[::]:{port}")

        # Set up signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

        # Start server
        server.start()
        logger.info("Communication service started with optimizations", port=port, max_workers=50)
        logger.info("Health checking enabled")
        logger.info("Graceful shutdown handlers registered")

        # Keep server running
        server.wait_for_termination()

    except Exception as e:
        # Log error
        logger.error("Server startup failed", error=str(e))

        # Raise error
        raise

    finally:
        # Cleanup resources
        cleanup()


# Main entry point
if __name__ == "__main__":
    # Log the start message
    logger.info("Starting communication service")

    # Serve the communication service
    serve()
