# Communication Service

## Overview
The Communication Service is a backend gRPC server designed to manage conversations and messages between users and agents. It provides a robust API for creating, retrieving, deleting, and listing conversations and messages with support for authorization, pagination, and filtering. The service uses MongoDB for data persistence and is built with scalability and maintainability in mind.

## Features
- gRPC-based communication API with well-defined protobuf messages.
- Conversation management: create, get, delete, and list conversations.
- Message management within conversations: create, get, delete, and list messages.
- User authorization to ensure data privacy and security.
- Pagination and filtering support for efficient data retrieval.
- MongoDB as the primary data store.
- Health checking endpoint for service monitoring.
- Dockerized for containerized deployment.
- Kubernetes manifests for cloud-native deployment.
- CI/CD pipeline integration with GitLab for automated builds and deployments.

## Technology Stack
- Python 3.11
- gRPC (grpcio, grpcio-tools, grpcio-health-checking)
- MongoDB (mongoengine, pymongo)
- Poetry for dependency management
- Docker and Kubernetes for containerization and orchestration
- Google Cloud Platform for container registry and Kubernetes Engine
- Logging with structlog
- Configuration management with python-dotenv and pydantic

## Prerequisites
- Python 3.11
- Poetry 1.7.1
- Docker (for container builds)
- Kubernetes cluster (for deployment)
- Google Cloud SDK (for GKE deployment)
- MongoDB instance accessible by the service

## Installation and Setup

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd communication-service
   ```

2. Copy the environment variables template and configure:
   ```bash
   cp .env.example .env
   # Edit .env to set MongoDB connection details and other variables
   ```

3. Install dependencies using Poetry:
   ```bash
   poetry install --no-root
   ```

4. Generate gRPC code:
   ```bash
   poetry run python -m app.scripts.generate_grpc
   ```

## Running Locally

You can run the service locally using the provided script:

```bash
./run_local.sh
```

This script installs dependencies, generates gRPC code, and starts the gRPC server on the configured port (default 50055).

Alternatively, build and run the Docker container:

```bash
docker build -t communication-service .
docker run -p 50055:50055 --env-file .env communication-service
```

## gRPC API Overview

The service exposes the following main gRPC services:

- **CommunicationService**: Combines conversation and message services.
- **ConversationService**: Methods to create, get, delete, and list conversations.
- **MessageService**: Methods to create, get, delete, and list messages within conversations.
- **Health Checking Service**: For monitoring service health.

Refer to the protobuf definitions in the `app/proto` directory for detailed API specifications.

## Deployment

### CI/CD Pipeline

The project includes a GitLab CI/CD pipeline defined in `.gitlab-ci.yml` that automates:

- Authentication with Google Cloud.
- Building and pushing Docker images to Google Artifact Registry.
- Deploying or updating the Kubernetes deployment on Google Kubernetes Engine (GKE).
- Sending Slack notifications on build and deployment status.

### Kubernetes Deployment

The Kubernetes manifest `k8s-manifest-dev.yml` defines:

- A ServiceAccount for the service.
- Deployment with resource requests and limits.
- Service exposing the gRPC port.
- Ingress routing HTTP traffic to the service.
- Configurable container image with environment-specific tags.

To deploy manually:

```bash
kubectl apply -f k8s-manifest-dev.yml -n ruh-dev
```

Ensure your Kubernetes context is set to the target cluster and namespace.

## Configuration

The service uses environment variables for configuration. Key variables include:

- `APP_NAME`: Service name.
- `DEBUG`: Enable debug mode.
- `GRPC_PORT`: Port for the gRPC server.
- MongoDB connection details:
  - `MONGO_HOST`: MongoDB server hostname (or MongoDB Atlas cluster hostname)
  - `MONGO_PORT`: MongoDB server port (not used for MongoDB Atlas but still required)
  - `MONGO_DB_NAME`: Database name
  - `MONGO_USERNAME`: MongoDB username
  - `MONGO_PASSWORD`: MongoDB password
- Proto generation variables: `REPO_URL`, `GIT_TOKEN`, `ENV`.

### MongoDB Atlas Configuration

For MongoDB Atlas connections, use the following format for `MONGO_HOST`:
- Example: `cluster0.example.mongodb.net`

The service automatically detects MongoDB Atlas connections (hostname contains "mongodb.net") and uses the appropriate connection string format.

Refer to `.env.example` for the full list of environment variables.

## Logging

The service uses `structlog` for structured logging, facilitating easy integration with log aggregation systems.

## Contributing

Contributions are welcome. Please fork the repository and submit pull requests with clear descriptions and tests.

## License

Specify your license here.